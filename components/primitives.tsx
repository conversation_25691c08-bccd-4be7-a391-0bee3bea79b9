import { cn } from '@/utils/cn';
import { tv } from 'tailwind-variants';

interface SectionHeaderProps {
	header?: string;
	title: string;
	description?: string;
	sectionClass?: string;
	headerClass?: string;
	titleClass?: string;
	descriptionClass?: string;
}

export const SectionHeader: React.FC<SectionHeaderProps> = ({
	header = '',
	title,
	description,
	sectionClass,
	headerClass,
	titleClass,
	descriptionClass,
}) => {
	return (
		<div className={cn('z-50 flex w-full flex-col items-center', sectionClass)}>
			<span
				className={cn(
					'text-md font-semibold text-zeco-purple md:text-xl lg:text-2xl',
					headerClass
				)}
			>
				{header}
			</span>
			<h2
				className={cn(
					'bg-linear-to-b bg-clip-text py-2 text-center text-4xl font-bold md:text-5xl xl:pb-4 2xl:text-6xl',
					titleClass
				)}
			>
				{title}
			</h2>
			<p
				className={cn(
					'md:text-md text-center text-sm text-neutral-400 xl:text-xl',
					descriptionClass
				)}
			>
				{description}
			</p>
		</div>
	);
};

export const title = tv({
	base: 'tracking-tight inline font-semibold',
	variants: {
		color: {
			violet: 'from-[#FF1CF7] to-[#b249f8]',
			yellow: 'from-[#FF705B] to-[#FFB457]',
			blue: 'from-[#5EA2EF] to-[#0072F5]',
			cyan: 'from-[#00b7fa] to-[#01cfea]',
			green: 'from-[#6FEE8D] to-[#17c964]',
			pink: 'from-[#FF72E1] to-[#F54C7A]',
			foreground: 'dark:from-[#FFFFFF] dark:to-[#4B4B4B]',
		},
		size: {
			sm: 'text-3xl lg:text-4xl',
			md: 'text-[2.3rem] lg:text-5xl leading-9',
			lg: 'text-4xl lg:text-6xl',
			xl: 'text-6xl lg:text-8xl',
		},
		fullWidth: {
			true: 'w-full block',
		},
	},
	defaultVariants: {
		size: 'md',
	},
	compoundVariants: [
		{
			color: ['violet', 'yellow', 'blue', 'cyan', 'green', 'pink', 'foreground'],
			class: 'bg-clip-text text-transparent bg-linear-to-b',
		},
	],
});

export const subtitle = tv({
	base: 'w-full md:w-1/2 my-2 md:text-lg lg:text-xl text-default-800 block max-w-full',
	variants: {
		fullWidth: {
			true: 'w-full!',
		},
	},
	defaultVariants: {
		fullWidth: true,
	},
});
