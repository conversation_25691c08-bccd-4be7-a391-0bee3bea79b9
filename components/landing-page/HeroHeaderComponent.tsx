'use client';
import { memo, useMemo } from 'react';
import { motion } from 'framer-motion';
import SparklesCore from '@/components/ui/sparkles';
import HeroInteractiveDemo from './HeroInteractiveDemo';
import { <PERSON><PERSON>I, Gemini, Flux, Ideogram, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from '@lobehub/icons';

export const HeroHeaderComponent: React.FC = memo(() => {
	const particleSettings = useMemo(
		() => ({
			id: 'particles-background',
			background: 'transparent',
			minSize: 0.6,
			maxSize: 1.0,
			particleDensity: 60,
			particleColor: '#9455D3',
		}),
		[]
	);

	return (
		<div
			id="hero-section"
			className="relative flex h-full min-h-screen flex-col items-center justify-center overflow-hidden pb-8"
		>
			{/* Background Effects */}
			<div className="absolute inset-0 h-full w-full">
				<SparklesCore
					{...particleSettings}
					className="h-full w-full"
				/>
				{/* Fade overlay from top to bottom */}
				<div className="pointer-events-none absolute inset-0 bg-linear-to-b from-transparent via-transparent to-black" />
			</div>

			{/* Main Content */}
			<div className="relative z-10 flex flex-col items-center justify-center px-4 md:px-8">
				{/* Badge */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.5 }}
					className="mt-20 pt-12 sm:mt-0"
				>
					<div className="group relative inline-block cursor-pointer rounded-full bg-slate-800 p-px text-xs font-semibold leading-6 text-white no-underline shadow-2xl shadow-zinc-900">
						<span className="absolute inset-0 overflow-hidden rounded-full">
							<span className="absolute inset-0 rounded-full bg-[radial-gradient(75%_100%_at_50%_0%,rgba(148,85,211,0.6)_0%,rgba(148,85,211,0)_75%)] opacity-0 transition-opacity duration-500 group-hover:opacity-100" />
						</span>
						<div className="relative z-10 flex items-center space-x-2 rounded-full bg-zinc-950 px-4 py-0.5 ring-1 ring-white/10">
							<span>🚀 Launching soon !</span>
						</div>
						<span className="absolute -bottom-0 left-4.5 h-px w-[calc(100%-2.25rem)] bg-linear-to-r from-fuchsia-400/0 via-fuchsia-400/90 to-fuchsia-400/0 transition-opacity duration-500 group-hover:opacity-40" />
					</div>
				</motion.div>

				{/* Main Heading */}
				<div className="z-10 mx-auto mt-8 max-w-5xl text-center text-3xl font-bold sm:text-4xl md:text-7xl">
					{'One Subscription for Everything AI'.split(' ').map((word, index) => (
						<motion.span
							key={index}
							initial={{ opacity: 0, filter: 'blur(4px)', y: 10 }}
							animate={{ opacity: 1, filter: 'blur(0px)', y: 0 }}
							transition={{
								duration: 0.3,
								delay: index * 0.1,
								ease: 'easeInOut',
							}}
							className="mr-2 inline-block bg-linear-to-b bg-clip-text sm:py-1 md:py-2"
						>
							{word}
						</motion.span>
					))}
				</div>

				{/* Description */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.8, delay: 0.6 }}
					className="my-6 max-w-3xl pb-10 text-center text-sm text-neutral-400 sm:mb-8 sm:pb-4 sm:text-base md:pb-8 md:text-lg lg:pb-0"
				>
					<p className="flex flex-wrap items-center justify-center gap-x-2 gap-y-1">
						<span className="inline-flex items-center gap-1">
							<OpenAI size={18} />
							<span className="font-semibold text-white">ChatGPT,</span>
						</span>
						<span className="inline-flex items-center gap-1">
							<Gemini.Color size={18} />
							<span className="font-semibold text-white">Gemini,</span>
						</span>
						<span className="inline-flex items-center gap-1">
							<Claude.Color size={18} />
							<span className="font-semibold text-white">Claude,</span>
						</span>
						<span className="inline-flex items-center gap-1">
							<Flux size={18} />
							<span className="font-semibold text-white">FLUX,</span>
						</span>
						<span className="inline-flex items-center gap-1">
							<Ideogram size={18} />
							<span className="font-semibold text-white">Ideogram,</span>
						</span>
						<span className="inline-flex items-center gap-1">
							<Kling.Color size={18} />
							<span className="font-semibold text-white">Kling,</span>
						</span>
						<span className="inline-flex items-center gap-1">
							<DeepMind.Color size={18} />
							<span className="font-semibold text-white">Veo</span>
						</span>
						and 10+ premium models in a single subscription—no extra plans, ever.
					</p>
				</motion.div>

				{/* Interactive Demo */}
				<HeroInteractiveDemo />
			</div>
		</div>
	);
});

HeroHeaderComponent.displayName = 'HeroComponent';

export default HeroHeaderComponent;
