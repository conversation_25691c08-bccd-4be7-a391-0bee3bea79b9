'use client';

import { memo } from 'react';
import Link from 'next/link';
import { Accordion, AccordionItem } from '@heroui/accordion';
import { CrossIcon, PlusIcon } from '@/components/icons';
import { SectionHeader } from '@/components/primitives';
import ModelsOfferedModal from './ModelsOfferedModal';
import { CreditInfoModal } from './CreditInfoModal';
import { useDisclosure } from '@heroui/modal';
import { CREDIT_EXAMPLES, PricingPlanType } from './pricing';

const FAQComponent: React.FC = memo(() => {
	const { isOpen, onOpen, onOpenChange } = useDisclosure();
	const {
		isOpen: isCreditModalOpen,
		onOpen: onCreditModalOpen,
		onOpenChange: onCreditModalOpenChange,
	} = useDisclosure();

	const faqData = [
		{
			question: 'What is ZECO AI?',
			answer: "ZECO AI is an all-in-one AI platform designed to give you access to the world's best AI models through a single subscription. It eliminates the need to manage separate accounts and payments for various AI services like ChatGPT, Grok, Ideogram, Kling and others by bringing them all under one powerful and intuitive interface.",
		},
		{
			question: 'Why should I choose ZECO AI over other platforms?',
			answer: 'ZECO AI replaces multiple expensive AI subscriptions with one simple plan. We offer a curated selection of only the best models and guide you to the right one for any task—from creative brainstorming to designing stunning visuals. Effortlessly switch between models with a single-click, all without losing your context.',
		},
		{
			question: 'How can I get access to ZECO AI?',
			answer: 'ZECO AI is currently in a closed beta, and we are rolling out access to new users. You can join the beta waitlist by clicking on any "Join the Beta" button on this page. We are sending out invites as quickly as possible!',
		},
		{
			question: 'What is included in a ZECO AI subscription?',
			answer: (
				<p>
					Your ZECO AI subscription includes unlimited access to our full range of
					language models under our{' '}
					<Link
						href="/terms"
						className="text-zeco-purple underline"
					>
						fair usage policy
					</Link>
					, plus a monthly allowance of credits for all image and video generations. Need
					more creative firepower? You can top up your credits anytime.
				</p>
			),
		},
		{
			question: 'How do credits work on ZECO?',
			answer: (
				<p>
					Your ZECO Pro plan comes with 500 credits that refresh monthly. They are your
					currency for all AI image and video generations. Different models use different
					amounts based on their capabilities.{' '}
					<button
						onClick={onCreditModalOpen}
						className="font-medium text-zeco-purple underline"
					>
						See exactly what 500 credits can create
					</button>
					, or top up anytime if you need more creative firepower.
				</p>
			),
		},
		{
			question: 'What are the models available on ZECO?',
			answer: (
				<p>
					We handpick only the best models in text and media. You can see the full list of
					our available models by clicking{' '}
					<button
						onClick={onOpen}
						className="text-zeco-purple underline"
					>
						here
					</button>
					.
				</p>
			),
		},
		{
			question: 'Are there any enterprise or custom plans available?',
			answer: (
				<p>
					Yes. We offer custom plans and enterprise support. Please schedule a call with
					the team{' '}
					<Link
						// TODO: Make a founders' calendly link - <EMAIL>.
						href="https://calendly.com/utkarsh-choubey"
						className="text-zeco-purple underline"
						target="_blank"
						rel="noopener noreferrer"
					>
						here
					</Link>
					.
				</p>
			),
		},
	];

	return (
		<section
			id="faq"
			className="py-12 md:my-16"
		>
			<div className="flex w-full justify-center px-4 md:px-12">
				<div className="flex max-w-7xl flex-col items-center">
					{/* Hero Content */}
					<SectionHeader
						sectionClass="my-12"
						header={'FAQs'}
						title={'Frequently Asked Questions'}
					/>

					<div className="z-50 flex w-[95vw] max-w-[95vw] justify-center overflow-hidden rounded-2xl bg-neutral-950 p-2 md:w-[80vw] lg:w-[70vw] lg:p-8 xl:w-[64vw]">
						<Accordion
							variant="light"
							className="w-full"
						>
							{faqData.map((item, index) => (
								<AccordionItem
									key={index.toString()}
									indicator={({ isOpen }) =>
										isOpen ? (
											<CrossIcon
												size={28}
												fill="#9544d3"
											/>
										) : (
											<PlusIcon
												fill="#9544d3"
												size={28}
											/>
										)
									}
									aria-label={`Accordion ${index + 1}`}
									title={
										<p className="break-words text-xs md:text-sm lg:text-lg">
											{item.question}
										</p>
									}
								>
									<div className="mr-4 overflow-hidden break-words text-xs text-neutral-400 md:mr-8 md:text-sm lg:text-lg">
										{item.answer}
									</div>
								</AccordionItem>
							))}
						</Accordion>
					</div>
					{/* email us section */}
					<p className="md:text-md mt-8 flex justify-center text-xs text-neutral-400 max-[360px]:text-[0.55rem] md:mt-12 xl:text-lg">
						Still have questions? Reach out to us at <span>&nbsp;</span>{' '}
						<Link
							href="mailto:<EMAIL>"
							className="text-zeco-purple"
						>
							{' '}
							<EMAIL>{' '}
						</Link>
					</p>
				</div>
			</div>
			<ModelsOfferedModal
				isOpen={isOpen}
				onOpenChange={onOpenChange}
			/>
			<CreditInfoModal
				isOpen={isCreditModalOpen}
				onOpenChange={onCreditModalOpenChange}
				creditInfo={CREDIT_EXAMPLES[PricingPlanType.PRO]}
			/>
		</section>
	);
});

FAQComponent.displayName = 'FAQComponent';
export default FAQComponent;
