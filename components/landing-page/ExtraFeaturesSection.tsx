import { cn } from '@/utils/cn';
import {
	IconRouteAltLeft,
	IconShare,
	IconSearch,
	IconBrain,
	IconLock,
	IconFile,
	IconBrush,
	IconTools,
} from '@tabler/icons-react';
import { SectionHeader } from '../primitives';
import { memo } from 'react';

const extraFeatures = [
	{
		title: 'Multi-Modality',
		description: 'Upload and interact directly with images, PDFs, and documents in your chat.',
		icon: <IconFile />,
	},
	{
		title: 'Prompt Enhancement',
		description:
			'Enhance your media generation prompts to suit the model requirements and get the best results.',
		icon: <IconBrush />,
	},
	{
		title: 'AI Media Tools',
		description:
			'Instantly remove backgrounds, upscale images, edit, and remix any of your media creations.',
		icon: <IconTools />,
	},
	{
		title: 'Seamless Sharing',
		description: 'Share chats and prompt workflows in a single click. No more copy-pasting.',
		icon: <IconShare />,
	},
	{
		title: 'Enterprise-Grade Security',
		description:
			'SOC 2-ready encryption means your data stays yours — always secure, always compliant.',
		icon: <IconLock />,
	},
	{
		title: 'Deep Research',
		description:
			'Get structured, in-depth reports synthesized from across the web to answer your most complex questions.',
		icon: <IconSearch />,
		comingSoon: true,
	},
	{
		title: 'Spaces: Shared Memory',
		description:
			'Group chats, files and results in a Space where context follows every conversation automatically.',
		icon: <IconBrain />,
		comingSoon: true,
	},
	{
		title: 'AI Workflows',
		description:
			'Chain models across steps, reuse and share — think Gemini ➜ GPT-4o ➜ Mistral in one visual flow.',
		icon: <IconRouteAltLeft />,
		comingSoon: true,
	},
];

export const ExtraFeaturesSection = memo(() => {
	return (
		<div className="relative">
			<SectionHeader
				header="More than an AI"
				title="A Complete Creative Suite"
				//description="The AI world is overwhelming. ZECO makes it effortless."
			/>
			<div className="relative z-10 mx-auto grid max-w-7xl grid-cols-1 py-10 md:grid-cols-2 lg:grid-cols-4">
				{extraFeatures.map((feature, index) => (
					<Feature
						key={feature.title}
						{...feature}
						index={index}
					/>
				))}
			</div>
		</div>
	);
});

ExtraFeaturesSection.displayName = 'ExtraFeaturesSection';

const Feature = memo(
	({
		title,
		description,
		icon,
		index,
		comingSoon,
	}: {
		title: string;
		description: string;
		icon: React.ReactNode;
		index: number;
		comingSoon?: boolean;
	}) => {
		return (
			<div
				className={cn(
					'group/feature relative flex flex-col py-10 dark:border-neutral-800 lg:border-r',
					(index === 0 || index === 4) && 'dark:border-neutral-800 lg:border-l',
					index < 4 && 'dark:border-neutral-800 lg:border-b'
				)}
			>
				{index < 4 && (
					<div className="pointer-events-none absolute inset-0 h-full w-full bg-linear-to-t from-neutral-100 to-transparent opacity-0 transition duration-200 group-hover/feature:opacity-100 dark:from-neutral-800" />
				)}
				{index >= 4 && (
					<div className="pointer-events-none absolute inset-0 h-full w-full bg-linear-to-b from-neutral-100 to-transparent opacity-0 transition duration-200 group-hover/feature:opacity-100 dark:from-neutral-800" />
				)}
				{comingSoon && (
					<div className="absolute right-4 top-4">
						<div className="h-fit w-fit rounded-2xl bg-zeco-purple px-2 py-1 text-xs text-white">
							Soon
						</div>
					</div>
				)}
				<div className="relative z-10 mb-4 px-10 text-neutral-600 dark:text-neutral-400">
					{icon}
				</div>
				<div className="relative z-10 mb-2 px-10 text-lg font-bold">
					<div className="absolute inset-y-0 left-0 h-6 w-1 origin-center rounded-br-full rounded-tr-full bg-neutral-300 transition-all duration-200 group-hover/feature:h-8 group-hover/feature:bg-zeco-purple dark:bg-neutral-700" />
					<span className="inline-block text-neutral-800 transition duration-200 group-hover/feature:translate-x-2 dark:text-neutral-100">
						{title}
					</span>
				</div>
				<p className="relative z-10 max-w-xs px-10 text-sm text-neutral-600 dark:text-neutral-300">
					{description}
				</p>
			</div>
		);
	}
);

Feature.displayName = 'Feature';
