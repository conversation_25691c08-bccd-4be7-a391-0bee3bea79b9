'use client';

import { useCallback, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { Button } from '@heroui/button';
import { IconCameraAi, IconMessageChatbot, IconVideo } from '@tabler/icons-react';
import { UserInSidebar } from './UserInSidebar';
import ChatHist<PERSON> from './ChatHistory';
import { useSidebarVisibility, useSidebarVisibilityUpdate } from '@/app/(post-auth)/providers';

const EDGE_THRESHOLD = 20;
const SIDEBAR_WIDTH = 280;
const CLOSE_DELAY_MS = 500; // 500ms delay before closing

export default function Sidebar() {
	const isVisible = useSidebarVisibility();
	const setIsVisible = useSidebarVisibilityUpdate();
	const closeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

	// Track mouse position to detect when it's at the left edge (desktop only)
	useEffect(() => {
		const handleMouseMove = (e: MouseEvent) => {
			// Only enable mouse hover on desktop (md and up, non-touch devices)
			if (
				window.matchMedia('(min-width: 768px) and (hover: hover) and (pointer: fine)')
					.matches
			) {
				if (e.clientX <= EDGE_THRESHOLD) {
					// Clear any pending close timeout when mouse enters the threshold
					if (closeTimeoutRef.current) {
						clearTimeout(closeTimeoutRef.current);
						closeTimeoutRef.current = null;
					}
					if (!isVisible) {
						setIsVisible(true);
					}
				}
			}
		};

		window.addEventListener('mousemove', handleMouseMove);
		return () => {
			window.removeEventListener('mousemove', handleMouseMove);
			// Clean up any pending timeout on unmount
			if (closeTimeoutRef.current) {
				clearTimeout(closeTimeoutRef.current);
			}
		};
	}, [isVisible, setIsVisible]);

	const handleMouseLeave = useCallback(() => {
		// Only enable mouse leave on desktop (md and up, non-touch devices)
		if (
			window.matchMedia('(min-width: 768px) and (hover: hover) and (pointer: fine)').matches
		) {
			// Set a timeout to close the sidebar after the delay
			closeTimeoutRef.current = setTimeout(() => {
				setIsVisible(false);
				closeTimeoutRef.current = null;
			}, CLOSE_DELAY_MS);
		}
	}, [setIsVisible]);

	const handleMouseEnter = useCallback(() => {
		// Clear any pending close timeout when mouse enters the sidebar
		if (closeTimeoutRef.current) {
			clearTimeout(closeTimeoutRef.current);
			closeTimeoutRef.current = null;
		}
	}, []);

	return (
		<motion.aside
			className="h-full overflow-hidden"
			initial={{ width: 0 }}
			animate={{ width: isVisible ? SIDEBAR_WIDTH : 0 }}
			transition={{ type: 'tween', duration: 0.3, ease: 'easeInOut' }}
			onMouseLeave={handleMouseLeave}
			onMouseEnter={handleMouseEnter}
		>
			<div className="flex h-full w-full flex-col overflow-hidden bg-[#090909] p-3">
				<div className="mt-1 flex flex-row justify-between">
					<Link
						href="/chat"
						prefetch={true}
					>
						<Button
							disableRipple
							size="sm"
							variant="light"
							startContent={
								<IconMessageChatbot
									className="text-blue-500"
									size={16}
								/>
							}
						>
							Chat
						</Button>
					</Link>
					<Link
						href="/image"
						prefetch={true}
					>
						<Button
							disableRipple
							size="sm"
							variant="light"
							startContent={
								<IconCameraAi
									className="text-purple-500"
									size={16}
								/>
							}
						>
							Images
						</Button>
					</Link>

					<Link
						href="/video"
						prefetch={true}
					>
						<Button
							disableRipple
							size="sm"
							variant="light"
							startContent={
								<IconVideo
									className="text-orange-500"
									size={16}
								/>
							}
						>
							Videos
						</Button>
					</Link>
				</div>
				<ChatHistory />
				<UserInSidebar />
			</div>
		</motion.aside>
	);
}
