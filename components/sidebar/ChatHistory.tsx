'use client';

import { useState, useEffect, useMemo, useCallback } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { But<PERSON> } from '@heroui/button';
import {
	Dropdown,
	DropdownTrigger,
	DropdownMenu,
	DropdownItem,
	DropdownSection,
} from '@heroui/dropdown';
import { Spinner } from '@heroui/spinner';
import { Tooltip } from '@heroui/tooltip';
import { IconHistory, IconFilter, IconMessage, IconPhoto, IconVideo } from '@tabler/icons-react';
import {
	useChatHistoryContext,
	useChatHistoryUpdateContext,
	useSidebarVisibility,
} from '@/app/(post-auth)/providers';
import ChatHistoryItem from './ChatHistoryItem';
import { ChatType } from '@/types/chat';

export default function ChatHistory() {
	const router = useRouter();
	const pathname = usePathname();
	const [activeFilter, setActiveFilter] = useState<string>('all');
	const [isDropdownOpen, setIsDropdownOpen] = useState(false);
	const isSidebarOpen = useSidebarVisibility();
	const chatHistory = useChatHistoryContext();
	const setChatHistory = useChatHistoryUpdateContext();

	// Close dropdown when sidebar closes (only for desktop)
	useEffect(() => {
		// On desktop, close dropdown when sidebar closes
		// On mobile, this component is unmounted when mobile sidebar closes
		if (
			typeof window !== 'undefined' &&
			window.innerWidth >= 768 &&
			!isSidebarOpen &&
			isDropdownOpen
		) {
			setIsDropdownOpen(false);
		}
	}, [isDropdownOpen, isSidebarOpen]);

	// Get appropriate color styling based on active filter
	const getFilterButtonStyle = useCallback(() => {
		switch (activeFilter) {
			case 'chat':
				return 'bg-blue-400/10 text-blue-400';
			case 'image':
				return 'bg-purple-400/10 text-purple-400';
			case 'video':
				return 'bg-orange-400/10 text-orange-400';
			default:
				return '';
		}
	}, [activeFilter]);

	const handleChatDelete = useCallback(
		(chatType: ChatType, chatId: string) => {
			if (chatHistory) {
				setChatHistory(chatHistory.filter((chat) => chat.chatId !== chatId));
				if (pathname === `/${chatType}/${chatId}`) {
					router.push('/chat');
				}
			}
		},
		[chatHistory, pathname, router, setChatHistory]
	);

	const filteredHistory = useMemo(() => {
		if (chatHistory) {
			chatHistory.sort(
				(a, b) =>
					new Date(b.lastModifiedAt).getTime() - new Date(a.lastModifiedAt).getTime()
			);
			return activeFilter !== 'all'
				? chatHistory.filter((chat) => chat.chatType === activeFilter)
				: chatHistory;
		} else {
			return [];
		}
	}, [activeFilter, chatHistory]);

	return (
		<div className="flex min-h-0 flex-1 flex-col overflow-hidden">
			{/* Header section - moved from ChatHistoryHeader */}
			<div className="mb-1 flex shrink-0 items-center justify-between border-b border-divider px-2 py-3">
				<div className="ml-1 flex items-center gap-1.5">
					<IconHistory
						size={16}
						className="text-foreground-500"
					/>
					<h3 className="cursor-default text-sm font-medium tracking-wide text-primary-text">
						Recent
					</h3>
				</div>

				<Dropdown
					placement="bottom-start"
					isOpen={isDropdownOpen}
					onOpenChange={setIsDropdownOpen}
				>
					<DropdownTrigger>
						<Button
							disableRipple
							isIconOnly
							size="sm"
							variant="light"
							className={`h-6 w-6 ${getFilterButtonStyle()}`}
						>
							<Tooltip
								content="Filter Chats"
								placement="bottom"
								size="sm"
								radius="sm"
								className="bg-gray-50 text-[#090909]"
								delay={500}
								closeDelay={0}
							>
								<IconFilter size={16} />
							</Tooltip>
						</Button>
					</DropdownTrigger>
					<DropdownMenu
						aria-label="Filter history"
						onAction={(key) => {
							setActiveFilter(key as string);
						}}
						selectionMode="single"
						selectedKeys={[activeFilter]}
						itemClasses={{
							title: 'text-xs',
						}}
					>
						<DropdownSection
							title="Filter History"
							showDivider
						>
							<DropdownItem
								key="all"
								startContent={
									<IconHistory
										size={16}
										className="text-foreground-500"
									/>
								}
								className="text-xs"
								title="Show everything"
							/>
						</DropdownSection>
						<DropdownSection title="Chat Type">
							<DropdownItem
								key="chat"
								startContent={
									<IconMessage
										size={16}
										className="text-blue-400"
									/>
								}
								title="Chat"
							/>
							<DropdownItem
								key="image"
								startContent={
									<IconPhoto
										size={16}
										className="text-purple-400"
									/>
								}
								title="Image"
							/>
							<DropdownItem
								key="video"
								startContent={
									<IconVideo
										size={16}
										className="text-orange-400"
									/>
								}
								title="Video"
							/>
						</DropdownSection>
					</DropdownMenu>
				</Dropdown>
			</div>

			{/* Chat history list section */}
			{chatHistory === null ? (
				<Spinner
					label="Loading Chat History"
					size="sm"
					classNames={{ base: 'mt-6 space-y-2', label: 'text-xs text-secondary-text' }}
				/>
			) : chatHistory.length === 0 ? (
				<div className="px-3 py-2 text-center text-xs text-secondary-text">
					No chat history
				</div>
			) : (
				<div className="min-h-0 flex-1 overflow-y-auto overflow-x-hidden">
					<div className="space-y-1">
						{filteredHistory.map((chat) => (
							<ChatHistoryItem
								key={chat.chatId}
								chat={chat}
								onDelete={handleChatDelete}
							/>
						))}

						{filteredHistory.length === 0 && (
							<div className="flex flex-col items-center justify-center px-2 py-4">
								<p className="text-center text-xs text-secondary-text">
									History does not exist
								</p>
								<Button
									size="sm"
									variant="light"
									className="mt-2 text-xs"
									onPress={() => setActiveFilter('all')}
								>
									Show all chats
								</Button>
							</div>
						)}
					</div>
				</div>
			)}
		</div>
	);
}
