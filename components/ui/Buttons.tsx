'use client';

import { memo, useState, useCallback } from 'react';
import { cn } from '@/utils/cn';
import FormComponent from '@/components/landing-page/FormComponent';
import { Button, ButtonProps } from '@heroui/button';
import Link from 'next/link';

interface CTAButtonProps {
	title: string;
	className?: string;
	arrowNeeded?: boolean;
}

// TODO: Create a new button which redirects to a link/ makes an API call on click.
// TODO: Make the onclick more generic.
export const CTAButton: React.FC<CTAButtonProps> = ({ title, className, arrowNeeded = false }) => {
	const [isModalOpen, setIsModalOpen] = useState(false);

	const handleShowModalCallback = useCallback(() => {
		setIsModalOpen(true);
	}, []);

	const handleCloseModal = useCallback(() => {
		setIsModalOpen(false);
	}, []);

	return (
		<div>
			<Button
				className={cn(
					'text-md my-2 flex h-14 w-48 cursor-pointer items-center justify-center rounded-2xl border-2 border-white bg-white from-fuchsia-500 via-purple-600 to-violet-700 py-3 font-semibold text-black hover:space-x-2 hover:border-none hover:bg-linear-to-r hover:py-3.5 hover:text-white sm:mr-2 sm:py-3.5 hover:sm:py-4',
					className
				)}
				onPress={handleShowModalCallback}
			>
				<span>{title}</span>
				{arrowNeeded && <span className="text-xl md:pb-0.5 md:text-2xl">&rarr;</span>}
			</Button>
			<FormComponent
				isOpen={isModalOpen}
				onClose={handleCloseModal}
			/>
		</div>
	);
};

CTAButton.displayName = 'JoinBetaCTAButton';

export const GetStartedCTAButton: React.FC<CTAButtonProps> = ({
	title,
	className,
	arrowNeeded = false,
}) => {
	return (
		<Link href="/login">
			<Button
				className={cn(
					'text-md my-2 flex h-14 w-48 cursor-pointer items-center justify-center rounded-2xl border-2 border-white bg-white from-fuchsia-500 via-purple-600 to-violet-700 py-3 font-semibold text-black hover:space-x-2 hover:border-none hover:bg-linear-to-r hover:py-3.5 hover:text-white sm:mr-2 sm:py-3.5 hover:sm:py-4',
					className
				)}
			>
				<span>{title}</span>
				{arrowNeeded && <span className="text-xl md:pb-0.5 md:text-2xl">&rarr;</span>}
			</Button>
		</Link>
	);
};

GetStartedCTAButton.displayName = 'GetStartedCTAButton';

export const ExploreButton: React.FC = memo(() => {
	const handleScroll = useCallback(() => {
		const section = document.getElementById('multi-model-section');
		const rect = section!!.getBoundingClientRect();
		window.scrollTo({
			top: window.scrollY + rect.top,
			behavior: 'smooth',
		});
	}, []);

	return (
		<Button
			className="text-md my-2 flex h-14 w-48 cursor-pointer items-center justify-center rounded-2xl border-2 border-white bg-transparent py-3 font-semibold text-white sm:ml-2 sm:py-3.5 hover:xl:bg-white hover:xl:text-black"
			onPress={handleScroll}
		>
			Explore
		</Button>
	);
});

ExploreButton.displayName = 'ExploreButton';

export const FormButton: React.FC<ButtonProps> = memo((props: ButtonProps) => {
	return (
		<Button
			className="text-md flex w-24 cursor-pointer items-center justify-center rounded-2xl border-2 border-white bg-white from-fuchsia-500 via-purple-600 to-violet-700 py-4 text-black hover:border-none hover:bg-linear-to-r hover:text-white sm:mr-2 sm:mt-1"
			{...props}
		>
			Submit
		</Button>
	);
});

FormButton.displayName = 'FormButton';
