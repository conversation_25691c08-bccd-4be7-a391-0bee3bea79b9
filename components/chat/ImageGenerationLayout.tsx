'use client';

import { useState, useRef, useEffect, useCallback, memo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Attachment, ChatMessage } from '@/types/chat';
import {
	useUserContext,
	useModelUpdateContext,
	useChatHistoryUpdateContext,
	useSharedChatUpdateContext,
	useMediaGenerationModeContext,
	useSelectedModelPromptDetailsUpdateContext,
	ModelType,
} from '@/app/(post-auth)/providers';
import PromptSection, { SubmitData } from './PromptSection';
import ImageGenerationPanel from './ImageGenerationPanel';
import ImageEditPanel from './ImageEditPanel';
import {
	getImageModelEndpoint,
	ImageModelDisplayName,
	imageModelDisplayNameToProviderMap,
} from '@/models/image/image-generation-models';
import { nanoid } from '@/lib';
import { FloatingDock } from '@/components/ui/FloatingDock';
import { Logo } from '@/components/icons';
import { TypewriterEffectSmooth } from '@/components/ui/TypeWriterEffect';
import { addToast } from '@heroui/toast';
import { cn } from '@/utils/cn';

export interface ImageGenerationLayoutProps extends React.ComponentProps<'div'> {
	id: string;
	messages: ChatMessage[];
	isShared?: boolean;
}

const ImageGenerationLayout = memo(
	({ id, messages: initialMessages, isShared = false }: ImageGenerationLayoutProps) => {
		const user = useUserContext();
		const setChatHistory = useChatHistoryUpdateContext();
		const updateActiveModel = useModelUpdateContext();
		const updateSelectedModelImageDetails = useSelectedModelPromptDetailsUpdateContext();

		const isImageEditMode = useMediaGenerationModeContext() === 'EDIT';
		const [imageToEdit, setImageToEdit] = useState<Attachment | null>(null);
		const [imagesAvailableForEdit, setImagesAvailableForEdit] = useState<Attachment[]>([]);

		const [input, setInput] = useState('');
		const [isPromptEnhancementInProgress, setIsPromptEnhancementInProgress] = useState(false);
		const [isLoading, setIsLoading] = useState(false);
		const [isSharedChat, setIsSharedChat] = useState<boolean>(isShared);
		const updateSharedChatDetails = useSharedChatUpdateContext();
		const [currentMessages, setCurrentMessages] = useState<ChatMessage[]>(initialMessages);
		const previousMessagesRef = useRef<ChatMessage[]>(currentMessages);
		const abortControllerRef = useRef<AbortController | null>(null);

		const saveSnapshot = useCallback(() => {
			previousMessagesRef.current = currentMessages;
		}, [currentMessages]);

		const getAbortController = useCallback(() => {
			if (!abortControllerRef.current || abortControllerRef.current.signal.aborted) {
				abortControllerRef.current = new AbortController();
			}
			return abortControllerRef.current;
		}, []);

		const handlePromptEnhancement = useCallback(
			async (modelName: ModelType) => {
				const model = modelName as ImageModelDisplayName;
				const prompt = input.trim();
				if (!prompt) {
					return;
				}
				try {
					setIsPromptEnhancementInProgress(true);
					const response = await fetch('/api/enhance-prompt', {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json',
						},
						body: JSON.stringify({ prompt, model }),
					});

					if (!response.ok) {
						throw new Error('Failed to enhance prompt');
					}

					const { enhancedPrompt } = await response.json();
					setInput(enhancedPrompt);
				} catch (error: any) {
					// console.error(`Failed to enhance prompt.\nSelected Model:${model}\n${error}`);

					addToast({
						title: 'Failed to enhance prompt',
						description:
							'Failed to enhance prompt. Please try again or proceed with your original prompt.',
						color: 'danger',
					});
				} finally {
					setIsPromptEnhancementInProgress(false);
				}
			},
			[input]
		);

		const handleSubmitPrompt = useCallback(
			async (modelName: ModelType, data: SubmitData) => {
				const model = modelName as ImageModelDisplayName;
				const prompt = input.trim();
				if (!prompt || (isImageEditMode && !imageToEdit)) {
					return;
				}

				saveSnapshot();

				const isNewChat = currentMessages.length === 0;

				updateSelectedModelImageDetails({
					prompt: '',
					params: undefined,
				});
				setInput('');
				const messageId = nanoid();
				const formData = new FormData();

				formData.append('chatId', id);
				formData.append('messageId', messageId);
				formData.append('prompt', prompt);
				formData.append('model', model);
				formData.append('isNewChat', String(isNewChat));
				formData.append('isSharedChat', String(isSharedChat));
				formData.append('isImageEdit', String(isImageEditMode));
				formData.append('requestType', 'submit');

				if (isSharedChat) {
					formData.append('sharedMessages', JSON.stringify(initialMessages));
				}

				let uploadedFiles: Attachment[] = [];
				if (isImageEditMode) {
					uploadedFiles = [imageToEdit!];
					formData.append('images', JSON.stringify(imageToEdit));
				} else if (data.files) {
					Array.from(data.files).forEach((file) => {
						uploadedFiles.push({
							type: file.type,
							name: file.name,
							url: URL.createObjectURL(file),
						});
						formData.append('images', file);
					});
				}

				if (data.params) {
					formData.append('params', JSON.stringify(data.params));
				}

				const newMessage: ChatMessage = {
					chatId: id,
					id: messageId,
					prompt: {
						text: prompt,
						attachments: uploadedFiles,
						params: data.params,
					},
					response: null,
					createdAt: new Date().toISOString(),
				};

				setCurrentMessages((prev) => [...prev, newMessage]);
				setIsLoading(true);

				try {
					const controller = getAbortController();
					const fetchConfig = {
						method: 'POST',
						body: formData,
						signal: controller.signal,
						headers: {
							// Don't set Content-Type header when sending FormData
							// The browser will automatically set the correct boundary
						},
					};
					const response = await fetch('/api/image', fetchConfig);

					const { generatedImageResults, uploadedImageUrls, error } =
						await response.json();

					if (error) {
						throw new Error(error);
					}

					if (isImageEditMode) {
						setImagesAvailableForEdit((prev) => [...prev, generatedImageResults[0]]);
					}

					// Update message with response
					setCurrentMessages((prev) =>
						prev.map((msg) =>
							msg.id === messageId
								? {
										...msg,
										prompt: {
											...msg.prompt,
											attachments: uploadedFiles.map((attachment, index) => ({
												...attachment,
												url: uploadedImageUrls[index],
											})),
										},
										response: [
											{
												images: generatedImageResults,
												createdAt: new Date().toISOString(),
												modelUsed: {
													modelName: getImageModelEndpoint(
														model,
														uploadedFiles.length > 0
													),
													modelProvider:
														imageModelDisplayNameToProviderMap[model],
												},
											},
										],
									}
								: msg
						)
					);

					const currentTime = new Date().toISOString();
					if (isNewChat || isSharedChat) {
						window.history.replaceState(null, '', `${origin}/image/${id}`);

						// Create new chat history
						setChatHistory((existingChatHistory) => [
							{
								chatId: id,
								chatType: 'image',
								title: input,
								userId: user.id,
								historyContext: '',
								createdAt: currentTime,
								lastModifiedAt: currentTime,
							},
							...(existingChatHistory || []),
						]);
						setIsSharedChat(false);
					} else {
						// Update last modified time of current chat history
						setChatHistory((existingChatHistory) =>
							(existingChatHistory || []).map((chatHistory) =>
								chatHistory.chatId === id
									? {
											...chatHistory,
											lastModifiedAt: currentTime,
										}
									: chatHistory
							)
						);
					}
				} catch (error) {
					if (error instanceof DOMException && error.name === 'AbortError') {
						// console.log('Request aborted by the user.');
					} else {
						// Parse error for user-friendly message
						let errorMessage = 'Failed to generate image. Please try again.';
						try {
							const errorData = JSON.parse((error as Error).message);
							errorMessage = errorData.message || errorMessage;
						} catch {
							// If error.message is not JSON, keep default message
						}

						addToast({
							title: 'Error generating image',
							description: errorMessage,
							color: 'danger',
						});
					}
					setCurrentMessages(previousMessagesRef.current);
					setInput(prompt);
				} finally {
					setIsLoading(false);
				}
			},
			[
				currentMessages.length,
				getAbortController,
				id,
				input,
				isSharedChat,
				initialMessages,
				saveSnapshot,
				setChatHistory,
				user.id,
				updateSelectedModelImageDetails,
				isImageEditMode,
				imageToEdit,
			]
		);

		const handleEditPrompt = useCallback(
			async (
				model: ImageModelDisplayName,
				messageId: string,
				modifiedPrompt: string,
				data: SubmitData
			) => {
				saveSnapshot();
				const formData = new FormData();

				formData.append('chatId', id);
				formData.append('prompt', modifiedPrompt);
				formData.append('model', model);
				formData.append('requestType', 'edit');
				formData.append('messageId', messageId);

				if (data.existingAttachments) {
					data.existingAttachments.forEach((attachment) => {
						formData.append('images', JSON.stringify(attachment));
					});
				}

				// First update the prompt locally
				setCurrentMessages((prev) =>
					prev.map((msg) =>
						msg.id === messageId
							? {
									...msg,
									prompt: {
										...msg.prompt,
										text: modifiedPrompt,
									},
									response: null, // Clear existing response while we wait for new one
								}
							: msg
					)
				);

				setIsLoading(true);

				try {
					const controller = getAbortController();
					const fetchConfig = {
						method: 'POST',
						body: formData,
						signal: controller.signal,
						headers: {
							// Don't set Content-Type header when sending FormData
							// The browser will automatically set the correct boundary
						},
					};
					const response = await fetch('/api/image', fetchConfig);

					const { generatedImageResults, error } = await response.json();

					if (error) {
						throw new Error(error);
					}

					// Update message with new response
					setCurrentMessages((prev) =>
						prev.map((msg) =>
							msg.id === messageId
								? {
										...msg,
										response: [
											{
												images: generatedImageResults,
												createdAt: new Date().toISOString(),
												modelUsed: {
													modelName: getImageModelEndpoint(
														model,
														!!data.existingAttachments
													),
													modelProvider:
														imageModelDisplayNameToProviderMap[model],
												},
											},
										],
									}
								: msg
						)
					);

					// Update last modified time of current chat history
					setChatHistory((existingChatHistory) =>
						(existingChatHistory || []).map((chatHistory) =>
							chatHistory.chatId === id
								? {
										...chatHistory,
										lastModifiedAt: new Date().toISOString(),
									}
								: chatHistory
						)
					);
				} catch (error) {
					if (error instanceof DOMException && error.name === 'AbortError') {
						// console.log('Request aborted by the user.');
					} else {
						// Parse error for user-friendly message
						let errorMessage = 'Failed to edit and regenerate image. Please try again.';
						try {
							const errorData = JSON.parse((error as Error).message);
							errorMessage = errorData.message || errorMessage;
						} catch {
							// If error.message is not JSON, keep default message
						}

						addToast({
							title: 'Error editing prompt',
							description: errorMessage,
							color: 'danger',
						});
					}
					setCurrentMessages(previousMessagesRef.current);
				} finally {
					setIsLoading(false);
				}
			},
			[getAbortController, id, saveSnapshot, setChatHistory]
		);

		const handleRegenerateResponse = useCallback(
			async (messageId: string, model: ImageModelDisplayName, data: SubmitData) => {
				saveSnapshot();
				const message = currentMessages.find((msg) => msg.id === messageId);
				if (!message) {
					return;
				}

				const formData = new FormData();
				formData.append('chatId', id);
				formData.append('prompt', message.prompt.text);
				formData.append('model', model);
				formData.append('requestType', 'regenerate');
				formData.append('messageId', messageId);

				if (data.existingAttachments) {
					data.existingAttachments.forEach((attachment) => {
						formData.append('images', JSON.stringify(attachment));
					});
				}

				if (data.params) {
					formData.append('params', JSON.stringify(data.params));
				}

				// TODO: Store the params in placeholder response.
				setCurrentMessages((prev) =>
					prev.map((msg) =>
						msg.id === messageId
							? {
									...msg,
									prompt: {
										...msg.prompt,
										params: data.params,
									},
								}
							: msg
					)
				);

				setIsLoading(true);

				try {
					const controller = getAbortController();
					const fetchConfig = {
						method: 'POST',
						body: formData,
						signal: controller.signal,
						headers: {
							// Don't set Content-Type header when sending FormData
							// The browser will automatically set the correct boundary
						},
					};
					const response = await fetch('/api/image', fetchConfig);

					const { generatedImageResults, error } = await response.json();

					if (error) {
						throw new Error(error);
					}

					// Add new response to the message's response array
					setCurrentMessages((prev) =>
						prev.map((msg) =>
							msg.id === messageId
								? {
										...msg,
										response: [
											...(msg.response?.map((resp) => {
												return { ...resp, isActive: false };
											}) || []),
											{
												images: generatedImageResults,
												createdAt: new Date().toISOString(),
												modelUsed: {
													modelName: getImageModelEndpoint(
														model,
														!!data.existingAttachments
													),
													modelProvider:
														imageModelDisplayNameToProviderMap[model],
												},
											},
										],
									}
								: msg
						)
					);

					// Update last modified time of current chat history
					setChatHistory((existingChatHistory) =>
						(existingChatHistory || []).map((chatHistory) =>
							chatHistory.chatId === id
								? {
										...chatHistory,
										lastModifiedAt: new Date().toISOString(),
									}
								: chatHistory
						)
					);

					updateActiveModel(model);
					setInput('');
				} catch (error) {
					if (error instanceof DOMException && error.name === 'AbortError') {
						// console.log('Request aborted by the user.');
					} else {
						// Parse error for user-friendly message
						let errorMessage = 'Failed to regenerate image. Please try again.';
						try {
							const errorData = JSON.parse((error as Error).message);
							errorMessage = errorData.message || errorMessage;
						} catch {
							// If error.message is not JSON, keep default message
						}

						addToast({
							title: 'Error regenerating image',
							description: errorMessage,
							color: 'danger',
						});
					}
					setCurrentMessages(previousMessagesRef.current);
				} finally {
					setIsLoading(false);
				}
			},
			[
				currentMessages,
				getAbortController,
				id,
				saveSnapshot,
				setChatHistory,
				updateActiveModel,
			]
		);

		const handleNavigate = useCallback((messageId: string, targetIndex: number) => {
			setCurrentMessages((prev) =>
				prev.map((msg) =>
					msg.id === messageId
						? {
								...msg,
								response:
									msg.response?.map((response, index) => {
										if (index === targetIndex) {
											return { ...response, isActive: true };
										} else {
											return { ...response, isActive: false };
										}
									}) || null,
							}
						: msg
				)
			);
		}, []);

		const handleStop = useCallback(() => {
			if (abortControllerRef.current) {
				abortControllerRef.current.abort();
			}
		}, []);

		useEffect(() => {
			updateSharedChatDetails({
				type: 'image',
				chatId: id,
				isShareable: !isSharedChat && currentMessages.length > 0,
			});
		}, [currentMessages.length, id, isSharedChat, updateSharedChatDetails]);

		useEffect(() => {
			if (isImageEditMode) {
				updateActiveModel(ImageModelDisplayName.FLUX_KONTEXT_PRO);
			} else {
				updateActiveModel(ImageModelDisplayName.Stable_Diffusion_XL);
				setImageToEdit(null);
				setImagesAvailableForEdit([]);
			}
		}, [isImageEditMode, updateActiveModel]);

		const ImageEditPanelComponent = (
			<ImageEditPanel
				isImageEditInProgress={isLoading}
				imagesAvailableForEdit={imagesAvailableForEdit}
				setImageToEdit={setImageToEdit}
				setImagesAvailableForEdit={setImagesAvailableForEdit}
			/>
		);

		const PromptSectionComponent = (
			<PromptSection
				isDisabled={isImageEditMode && !imageToEdit}
				type="image"
				input={input}
				isLoading={isLoading}
				setInput={setInput}
				onSubmit={handleSubmitPrompt}
				isPromptEnhancementInProgress={isPromptEnhancementInProgress}
				onPromptEnhancement={handlePromptEnhancement}
				stop={handleStop}
			/>
		);

		return (
			<div className="flex h-full w-full max-w-3xl flex-col">
				<AnimatePresence mode="wait">
					{currentMessages.length === 0 ? (
						<motion.div
							key="dashboard"
							className="flex h-full flex-col"
							exit={{ opacity: 0 }}
							transition={{ duration: 0.2 }}
						>
							<div className="mb-12 flex flex-1 flex-col items-center justify-center space-y-8">
								{isImageEditMode ? (
									ImageEditPanelComponent
								) : (
									<div className="flex flex-col items-center justify-center space-y-4">
										<div className="group relative rounded-2xl border-1.5 border-zeco-purple bg-black p-3 transition duration-300 ease-in-out">
											<Logo
												size={36}
												fill="#ccc"
												className="flex md:hidden"
											/>
											<Logo
												size={48}
												fill="#ccc"
												className="hidden md:flex"
											/>
										</div>
										<TypewriterEffectSmooth
											words={[
												{
													text: 'Everything',
													className: 'text-zinc-800 dark:text-zinc-200',
												},
												{
													text: 'AI',
													className: 'text-zinc-800 dark:text-zinc-200',
												},
												{
													text: 'for',
													className: 'text-zinc-800 dark:text-zinc-200',
												},
												{
													text: 'YOU.',
													className: 'text-gradient-primary font-bold',
												},
											]}
											className="text-center"
										/>
									</div>
								)}

								<div className="mx-auto w-full max-w-2xl px-4 sm:px-0">
									{PromptSectionComponent}
								</div>
							</div>

							{!isImageEditMode && (
								<div className="mb-6 flex w-full items-center justify-center">
									<FloatingDock />
								</div>
							)}
						</motion.div>
					) : (
						<motion.div
							key="image"
							className="flex h-full w-full flex-1 flex-col"
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							exit={{ opacity: 0, y: -20 }}
							transition={{ duration: 0.2, ease: 'easeInOut' }}
						>
							<main
								className={cn(
									'flex w-full flex-1 flex-col overflow-y-auto',
									isImageEditMode && 'mb-8'
								)}
							>
								{isImageEditMode ? (
									ImageEditPanelComponent
								) : (
									<ImageGenerationPanel
										messages={currentMessages}
										isLoading={isLoading}
										isSharedChat={isSharedChat}
										onEdit={handleEditPrompt}
										onRegenerate={handleRegenerateResponse}
										onNavigate={handleNavigate}
									/>
								)}
							</main>
							<footer className="flex w-full flex-col px-6 pb-4">
								{PromptSectionComponent}
								<div className="relative mt-2 w-full">
									<p className="text-center text-xs text-white/40">
										These models can generate inaccurate images. Consider
										reviewing them before use.
									</p>
								</div>
							</footer>
						</motion.div>
					)}
				</AnimatePresence>
			</div>
		);
	}
);

ImageGenerationLayout.displayName = 'ImageGenerationLayout';
export default ImageGenerationLayout;
