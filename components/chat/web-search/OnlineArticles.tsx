import { motion } from 'framer-motion';
import Link from 'next/link';
import { IconExternalLink, IconUser, IconQuote } from '@tabler/icons-react';
import { ScholarlyArticles as ScholarlyArticlesType } from '@/types/web-search-results';
import { Card, CardBody, CardFooter } from '@heroui/card';
import { Chip } from '@heroui/chip';

interface OnlineArticlesProps {
	results: ScholarlyArticlesType;
}

export const OnlineArticles: React.FC<OnlineArticlesProps> = ({ results }) => {
	if (!results.articles?.length) {
		return null;
	}

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.3 }}
		>
			<Card
				disableRipple
				radius="lg"
				classNames={{
					base: 'bg-transparent',
					body: 'space-y-4',
					footer: 'flex flex-row items-center justify-end',
				}}
			>
				<CardBody>
					{results.articles.map((article, index) => (
						<motion.div
							key={`${article.title}-${index}`}
							initial={{ opacity: 0, x: -20 }}
							animate={{ opacity: 1, x: 0 }}
							transition={{ duration: 0.3, delay: index * 0.1 }}
						>
							<Card
								disableRipple
								isPressable
								as={Link}
								href={article.link}
								target="_blank"
								rel="noopener noreferrer"
								radius="lg"
								classNames={{
									base: 'bg-[#101010] hover:bg-sidebar-hover/50 hover:scale-[1.02] transition-all duration-300',
									body: 'flex p-4 space-y-3',
								}}
							>
								<CardBody>
									{/* Title Row */}
									<div className="flex items-center justify-between gap-2">
										<h4 className="line-clamp-2 flex-1 text-sm font-medium text-primary-text">
											{article.title}
										</h4>
										<IconExternalLink
											size={14}
											className="text-secondary-text/40"
										/>
									</div>

									{/* Metadata Row */}
									<div className="flex flex-wrap items-center gap-3 text-xs text-secondary-text/80">
										{article.author && (
											<div className="flex items-center gap-1">
												<IconUser
													size={12}
													className="shrink-0 text-zeco-purple/80"
												/>
												<span>{article.author}</span>
											</div>
										)}
										{article.cited_by && (
											<div className="flex items-center gap-1">
												<IconQuote
													size={12}
													className="shrink-0 text-zeco-purple/80"
												/>
												<span>{article.cited_by}</span>
											</div>
										)}
									</div>

									{/* Tags Row */}
									{article.highlighted_words &&
										article.highlighted_words.length > 0 && (
											<div className="flex flex-wrap gap-2">
												{article.highlighted_words.map((word, i) => (
													<Chip
														key={i}
														variant="flat"
														color="default"
														size="sm"
														radius="lg"
														classNames={{
															base: 'bg-zeco-purple/10',
															content: 'text-zeco-purple/80',
														}}
													>
														{word}
													</Chip>
												))}
											</div>
										)}
								</CardBody>
							</Card>
						</motion.div>
					))}
				</CardBody>
				<CardFooter>
					{results.link && (
						<Link
							href={results.link}
							target="_blank"
							rel="noopener noreferrer"
							className="flex items-center gap-1 text-xs text-secondary-text/80 transition-all duration-300 hover:text-zeco-purple"
						>
							View All
							<IconExternalLink size={12} />
						</Link>
					)}
				</CardFooter>
			</Card>
		</motion.div>
	);
};
