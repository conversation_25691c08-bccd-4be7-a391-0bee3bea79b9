import { useRef } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { Card, CardBody } from '@heroui/card';
import { Button } from '@heroui/button';
import {
	IconCalendarEvent,
	IconMapPin,
	IconExternalLink,
	IconChevronsRight,
	IconChevronsLeft,
} from '@tabler/icons-react';
import { Events as EventsType } from '@/types/web-search-results';

interface OnlineEventsFoundProps {
	results: EventsType[];
}

export const OnlineEventsFound: React.FC<OnlineEventsFoundProps> = ({ results }) => {
	const scrollContainerRef = useRef<HTMLDivElement>(null);

	if (!results.length) {
		return null;
	}

	const scroll = (direction: 'left' | 'right') => {
		const container = scrollContainerRef.current;
		if (!container) return;

		const scrollAmount = container.clientWidth * 0.8;
		container.scrollBy({
			left: direction === 'left' ? -scrollAmount : scrollAmount,
			behavior: 'smooth',
		});
	};

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.3 }}
			className="w-full"
		>
			<Card
				disableRipple
				radius="lg"
				classNames={{
					base: 'bg-transparent w-full',
					body: 'relative',
				}}
			>
				<CardBody>
					<Button
						isIconOnly
						disableRipple
						variant="flat"
						radius="full"
						onPress={() => scroll('left')}
						className="absolute left-0 top-1/2 z-10 -translate-y-1/2 bg-[#101010]"
					>
						<IconChevronsLeft
							size={20}
							className="text-zeco-purple/80"
						/>
					</Button>
					<div
						ref={scrollContainerRef}
						className="flex w-full gap-8 overflow-x-auto scroll-smooth px-4 scrollbar-hide"
					>
						{results.map((event, index) => (
							<motion.div
								key={`${event.title}-${index}`}
								initial={{ opacity: 0, y: 20 }}
								animate={{ opacity: 1, y: 0 }}
								transition={{ duration: 0.3, delay: index * 0.1 }}
								className="w-[280px] flex-none"
							>
								<Card
									disableRipple
									isPressable
									as={Link}
									href={event.link}
									target="_blank"
									rel="noopener noreferrer"
									radius="lg"
									classNames={{
										base: 'transition-all duration-300 hover:shadow-2xl hover:translate-y-[-2px] h-full bg-[#101010] hover:bg-sidebar-hover/50',
										body: 'p-0',
									}}
								>
									<CardBody>
										{event.thumbnail && (
											<div className="relative h-40 w-full overflow-hidden">
												<Image
													src={event.thumbnail}
													alt={event.title}
													fill
													className="object-cover transition-transform duration-500 hover:scale-110"
												/>
											</div>
										)}
										<div className="p-4">
											<div className="flex items-center justify-between gap-2">
												<div className="flex-1">
													<h4 className="line-clamp-2 text-sm font-medium text-primary-text">
														{event.title}
													</h4>
												</div>
												<IconExternalLink
													size={14}
													className="text-secondary-text/40"
												/>
											</div>
											<div className="mt-3 space-y-2">
												{event.date && (
													<div className="flex items-center gap-2 text-xs text-secondary-text/80">
														<IconCalendarEvent
															size={12}
															className="shrink-0 text-zeco-purple/80"
														/>
														<span>
															{event.date.when ||
																event.date.start_date}
														</span>
													</div>
												)}

												{event.address && event.address.length > 0 && (
													<div className="flex items-start gap-2 text-xs text-secondary-text/80">
														<IconMapPin
															size={12}
															className="mt-1 shrink-0 text-zeco-purple/80"
														/>
														<span className="line-clamp-2">
															{event.address.join(', ')}
														</span>
													</div>
												)}
											</div>
										</div>
									</CardBody>
								</Card>
							</motion.div>
						))}
					</div>
					<Button
						isIconOnly
						disableRipple
						radius="full"
						variant="flat"
						onPress={() => scroll('right')}
						className="absolute right-0 top-1/2 z-10 -translate-y-1/2 bg-[#101010]"
					>
						<IconChevronsRight
							size={20}
							className="text-zeco-purple/80"
						/>
					</Button>
				</CardBody>
			</Card>
		</motion.div>
	);
};
