import { useRef } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { Card, CardBody } from '@heroui/card';
import { Button } from '@heroui/button';
import {
	IconExternalLink,
	IconChevronsRight,
	IconChevronsLeft,
	IconPlayerPlay,
	IconUser,
	IconBrandYoutube,
} from '@tabler/icons-react';
import { InlineVideos as InlineVideosType } from '@/types/web-search-results';

interface OnlineVideosFoundProps {
	results: InlineVideosType[];
}

export const OnlineVideosFound: React.FC<OnlineVideosFoundProps> = ({ results }) => {
	const scrollContainerRef = useRef<HTMLDivElement>(null);

	if (!results.length) {
		return null;
	}

	const scroll = (direction: 'left' | 'right') => {
		const container = scrollContainerRef.current;
		if (!container) return;

		const scrollAmount = container.clientWidth * 0.8;
		container.scrollBy({
			left: direction === 'left' ? -scrollAmount : scrollAmount,
			behavior: 'smooth',
		});
	};

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.3 }}
			className="w-full"
		>
			<Card
				disableRipple
				radius="lg"
				classNames={{
					base: 'w-full bg-transparent border-none shadow-none',
					body: 'relative',
				}}
			>
				<CardBody>
					<Button
						isIconOnly
						radius="full"
						variant="flat"
						onPress={() => scroll('left')}
						className="absolute left-0 top-1/2 z-10 -translate-y-1/2 bg-[#101010]"
					>
						<IconChevronsLeft
							size={20}
							className="text-zeco-purple/80"
						/>
					</Button>
					<div
						ref={scrollContainerRef}
						className="flex w-full gap-4 overflow-x-auto scroll-smooth px-4 scrollbar-hide"
					>
						{results.map((video, index) => (
							<motion.div
								key={`${video.title}-${index}`}
								initial={{ opacity: 0, y: 20 }}
								animate={{ opacity: 1, y: 0 }}
								transition={{ duration: 0.3, delay: index * 0.1 }}
								className="w-[280px] flex-none"
							>
								<Card
									disableRipple
									isPressable
									as={Link}
									href={video.link}
									target="_blank"
									rel="noopener noreferrer"
									radius="lg"
									classNames={{
										base: 'border-white/5 transition-all duration-300 hover:shadow-2xl hover:translate-y-[-2px] h-full bg-[#101010] hover:bg-[#1a1a1a]',
										body: 'p-0',
									}}
								>
									<CardBody>
										<div className="relative aspect-video overflow-hidden">
											{video.thumbnail && (
												<Image
													src={video.thumbnail}
													alt={video.title}
													fill
													className="object-cover transition-transform duration-500 hover:scale-110"
												/>
											)}
											{video.duration && (
												<div className="absolute bottom-2 right-2 flex items-center gap-1 rounded-full bg-black/80 px-2 py-1 text-xs text-white">
													<IconPlayerPlay
														size={12}
														className="shrink-0 text-zeco-purple/80"
													/>
													{video.duration}
												</div>
											)}
										</div>
										<div className="p-4">
											<div className="flex items-start justify-between gap-2">
												<div className="flex-1 space-y-1">
													<h4 className="line-clamp-2 text-sm font-medium text-primary-text">
														{video.title}
													</h4>
													<div className="flex items-center gap-2">
														{video.channel && (
															<div className="flex items-center gap-1">
																<IconUser
																	size={12}
																	className="shrink-0 text-zeco-purple/80"
																/>
																<span className="text-xs text-secondary-text/80">
																	{video.channel}
																</span>
															</div>
														)}
														{video.platform && (
															<>
																<span className="text-xs text-secondary-text/40">
																	•
																</span>
																<div className="flex items-center gap-1">
																	<IconBrandYoutube
																		size={12}
																		className="shrink-0 text-zeco-purple/80"
																	/>
																	<span className="text-xs text-secondary-text/80">
																		{video.platform}
																	</span>
																</div>
															</>
														)}
													</div>
												</div>
												<IconExternalLink
													size={14}
													className="mt-1 shrink-0 text-secondary-text/40"
												/>
											</div>
											{video.date && (
												<div className="mt-3 text-xs text-secondary-text/60">
													{video.date}
												</div>
											)}
										</div>
									</CardBody>
								</Card>
							</motion.div>
						))}
					</div>
					<Button
						isIconOnly
						radius="full"
						variant="flat"
						onPress={() => scroll('right')}
						className="absolute right-0 top-1/2 z-10 -translate-y-1/2 bg-[#101010]"
					>
						<IconChevronsRight
							size={20}
							className="text-zeco-purple/80"
						/>
					</Button>
				</CardBody>
			</Card>
		</motion.div>
	);
};
