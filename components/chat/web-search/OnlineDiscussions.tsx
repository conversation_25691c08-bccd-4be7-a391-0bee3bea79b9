import Link from 'next/link';
import { motion } from 'framer-motion';
import { Card, CardBody } from '@heroui/card';
import { Chip } from '@heroui/chip';
import { IconExternalLink, IconClock, IconMessage, IconSpeakerphone } from '@tabler/icons-react';
import { DiscussionsAndForums as DiscussionsAndForumsType } from '@/types/web-search-results';

interface OnlineDiscussionsProps {
	results: DiscussionsAndForumsType[];
}

export const OnlineDiscussions: React.FC<OnlineDiscussionsProps> = ({ results }) => {
	if (!results.length) {
		return null;
	}

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.3 }}
		>
			<Card
				disableRipple
				classNames={{
					base: 'bg-transparent',
					body: 'space-y-3',
				}}
				radius="lg"
			>
				<CardBody>
					{results.map((item, index) => (
						<motion.div
							key={`${item.title}-${index}`}
							initial={{ opacity: 0, x: -20 }}
							animate={{ opacity: 1, x: 0 }}
							transition={{ duration: 0.3, delay: index * 0.1 }}
						>
							<Card
								disableRipple
								isPressable
								as={Link}
								href={item.link}
								target="_blank"
								rel="noopener noreferrer"
								radius="lg"
								classNames={{
									base: 'bg-[#101010] hover:bg-sidebar-hover/50 hover:scale-[1.02] transition-all duration-300',
									body: 'p-4 flex flex-1 space-y-3',
								}}
							>
								<CardBody>
									{/* Title Row */}
									<div className="flex items-start justify-between gap-2">
										<h4 className="line-clamp-2 flex-1 text-sm font-medium text-primary-text">
											{item.title}
										</h4>
										<IconExternalLink
											size={14}
											className="text-secondary-text/40"
										/>
									</div>

									{/* Metadata Row */}
									<div className="flex flex-wrap items-center gap-3 text-xs text-secondary-text/80">
										<div className="flex items-center gap-1">
											<IconSpeakerphone
												size={12}
												className="shrink-0 text-zeco-purple/80"
											/>
											<span>{item.source}</span>
										</div>
										{item.date && (
											<div className="flex items-center gap-1">
												<IconClock
													size={12}
													className="shrink-0 text-zeco-purple/80"
												/>
												<span>{item.date}</span>
											</div>
										)}
									</div>
									{/* Tags Row */}
									{item.extensions?.length > 0 && (
										<div className="flex flex-wrap gap-2">
											{item.extensions.map((ext, i) => (
												<Chip
													key={i}
													variant="flat"
													color="default"
													size="sm"
													radius="lg"
													classNames={{
														base: 'bg-zeco-purple/10',
														content: 'text-zeco-purple/80',
													}}
												>
													{ext}
												</Chip>
											))}
										</div>
									)}
								</CardBody>
							</Card>
						</motion.div>
					))}
				</CardBody>
			</Card>
		</motion.div>
	);
};
