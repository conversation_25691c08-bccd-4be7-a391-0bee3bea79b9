import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { Card, CardBody } from '@heroui/card';
import { Chip } from '@heroui/chip';
import {
	IconStarFilled,
	IconExternalLink,
	IconBuildingStore,
	IconTruck,
} from '@tabler/icons-react';
import { ShoppingResults as ShoppingResultsType } from '@/types/web-search-results';

interface OnlineShoppingResultsProps {
	results: ShoppingResultsType[];
}

export const OnlineShoppingResults: React.FC<OnlineShoppingResultsProps> = ({ results }) => {
	if (!results.length) {
		return null;
	}

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.3 }}
		>
			<Card
				disableRipple
				radius="lg"
				classNames={{
					base: 'bg-transparent',
					body: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8',
				}}
			>
				<CardBody>
					{results.map((product: ShoppingResultsType, index: number) => (
						<motion.div
							key={`${product.title}-${index}`}
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ duration: 0.3, delay: index * 0.1 }}
						>
							<Card
								disableRipple
								isPressable
								as={Link}
								href={product.link}
								target="_blank"
								rel="noopener noreferrer"
								radius="lg"
								classNames={{
									base: 'bg-[#101010] hover:bg-sidebar-hover/50 hover:scale-[1.02] transition-all duration-300',
									body: 'p-0',
								}}
							>
								<CardBody>
									<div className="relative h-40 w-full overflow-hidden bg-card/20">
										<Image
											src={product.thumbnail}
											alt={product.title}
											fill
											className="object-cover transition-transform duration-300 hover:scale-110"
										/>
									</div>
									<div className="space-y-2 p-3">
										<div className="flex items-start justify-between gap-2">
											<div className="flex-1">
												<h4 className="line-clamp-2 text-sm font-medium text-primary-text">
													{product.title}
												</h4>
											</div>
											<IconExternalLink
												size={14}
												className="mt-1 shrink-0 text-secondary-text/40"
											/>
										</div>
										<div className="flex flex-wrap items-center gap-2 text-xs text-secondary-text/80">
											{product.source && (
												<div className="flex items-center gap-1">
													<IconBuildingStore
														size={12}
														className="shrink-0 text-zeco-purple/80"
													/>
													<span>{product.source}</span>
												</div>
											)}
											{product.rating && (
												<div className="flex items-center gap-1">
													<IconStarFilled
														size={12}
														className="shrink-0 text-yellow-400"
													/>
													<span className="text-xs text-primary-text">
														{product.rating}
													</span>
													{/* {product.reviews && (
														<span className="text-xs text-secondary-text/80">
															(
															{product.reviews_original ||
																`${product.reviews} reviews`}
															)
														</span>
													)} */}
												</div>
											)}
										</div>
										<div className="flex flex-wrap items-center gap-2 text-xs text-secondary-text/80">
											{product.price && (
												<div className="flex items-center gap-1">
													{/* <IconCurrency
														size={12}
														className="text-zeco-purple/80"
													/> */}
													<span className="text-xs font-medium text-primary-text">
														{product.price}
													</span>
													{product.old_price && (
														<span className="text-xs text-secondary-text/60 line-through">
															{product.old_price}
														</span>
													)}
												</div>
											)}
											{product.shipping && (
												<div className="flex items-center gap-1">
													<IconTruck
														size={12}
														className="shrink-0 text-zeco-purple/80"
													/>
													<span>{product.shipping}</span>
												</div>
											)}
										</div>
										{product.extensions?.length > 0 && (
											<div className="flex flex-row gap-1 overflow-x-auto">
												{product.extensions.map(
													(ext: string, i: number) => (
														<Chip
															key={i}
															variant="flat"
															color="default"
															size="sm"
															radius="lg"
															classNames={{
																base: 'bg-zeco-purple/10',
																content: 'text-zeco-purple/80',
															}}
														>
															{ext}
														</Chip>
													)
												)}
											</div>
										)}
									</div>
								</CardBody>
							</Card>
						</motion.div>
					))}
				</CardBody>
			</Card>
		</motion.div>
	);
};
