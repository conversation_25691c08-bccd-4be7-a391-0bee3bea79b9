'use client';

import { useState, useRef, useEffect, useCallback, memo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Attachment, ChatMessage, PollingTaskResponse, Response } from '@/types/chat';
import {
	ModelType,
	useUserContext,
	useChatHistoryUpdateContext,
	useSharedChatUpdateContext,
	useModelUpdateContext,
	useSelectedModelPromptDetailsUpdateContext,
} from '@/app/(post-auth)/providers';
import PromptSection, { SubmitData } from './PromptSection';
import { nanoid } from '@/lib';
import { FloatingDock } from '@/components/ui/FloatingDock';
import { Logo } from '@/components/icons';
import { TypewriterEffectSmooth } from '@/components/ui/TypeWriterEffect';
import {
	VideoModelDisplayName,
	VideoModelProvider,
	videoModelDisplayNameToProviderMap,
	videoModelDisplayNameToTechnicalNameMap,
} from '@/models/video/video-generation-models';
import VideoGenerationPanel from './VideoGenerationPanel';
import { addToast } from '@heroui/toast';

export interface VideoGenerationLayoutProps extends React.ComponentProps<'div'> {
	id: string;
	messages: ChatMessage[];
	isShared?: boolean;
}

const VideoGenerationLayout = memo(
	({ id, messages: initialMessages, isShared = false }: VideoGenerationLayoutProps) => {
		const user = useUserContext();
		const setChatHistory = useChatHistoryUpdateContext();
		const updateActiveModel = useModelUpdateContext();
		const updateSelectedModelPrompt = useSelectedModelPromptDetailsUpdateContext();

		const [input, setInput] = useState('');
		const [isPromptEnhancementInProgress, setIsPromptEnhancementInProgress] = useState(false);
		const [isLoading, setIsLoading] = useState(false);
		const [isSharedChat, setIsSharedChat] = useState<boolean>(isShared);
		const updateSharedChatDetails = useSharedChatUpdateContext();
		const [currentMessages, setCurrentMessages] = useState<ChatMessage[]>(initialMessages);
		const previousMessagesRef = useRef<ChatMessage[]>(currentMessages);
		const pollingTimerRef = useRef<NodeJS.Timeout | null>(null);
		const pollingCountRef = useRef<number>(0);
		const [isVideoResponseLoading, setIsVideoResponseLoading] = useState(false);

		const saveSnapshot = useCallback(() => {
			previousMessagesRef.current = currentMessages;
		}, [currentMessages]);

		const setUpPolling = useCallback(
			(
				prompt: string,
				messageId: string,
				requestId: string,
				model: VideoModelDisplayName,
				pollingInterval = 60000
			) => {
				pollingCountRef.current = 0;
				if (pollingTimerRef.current) {
					clearTimeout(pollingTimerRef.current);
					pollingTimerRef.current = null;
				}
				setIsVideoResponseLoading(true);

				const handlePollSuccess = (messageId: string, response: Response[]) => {
					setCurrentMessages((prev) =>
						prev.map((msg) =>
							msg.id === messageId
								? {
										...msg,
										response,
									}
								: msg
						)
					);
					setIsVideoResponseLoading(false);
				};

				const handlePollFailure = (reason: string) => {
					setCurrentMessages(previousMessagesRef.current);
					setInput(prompt);
					setIsVideoResponseLoading(false);
					addToast({
						title: 'Error generating video',
						description: `Model: ${model}. ${reason}`,
						color: 'danger',
					});
				};

				const pollForResponse = async () => {
					try {
						pollingCountRef.current += 1;

						if (pollingCountRef.current > 10) {
							if (pollingTimerRef.current) {
								clearTimeout(pollingTimerRef.current);
								pollingTimerRef.current = null;
							}
							// console.log('Maximum polling attempts reached');
							handlePollFailure('Video generation timed out. Please try again.');
							return;
						}

						const modelProvider = videoModelDisplayNameToProviderMap[model];
						let response: globalThis.Response;

						if (modelProvider === VideoModelProvider.Google) {
							response = await fetch('/api/video/vertex', {
								method: 'POST',
								headers: {
									'Content-Type': 'application/json',
								},
								body: JSON.stringify({ requestId }),
							});
						} else {
							response = await fetch(`/api/video/status?requestId=${requestId}`);
						}

						if (!response.ok) {
							return;
						}

						const responseData = await response.json();

						if (modelProvider === VideoModelProvider.Google) {
							// Handle Vertex AI response format
							const { status, error: vertexError } = responseData;

							console.log(
								`Polling attempt ${pollingCountRef.current} for request ${requestId}: Status = ${status}`
							);

							if (status === 'COMPLETED') {
								// For Vertex AI, the video is already processed and stored
								// We need to fetch the updated message from the status endpoint
								const statusResponse = await fetch(
									`/api/video/status?requestId=${requestId}`
								);
								if (statusResponse.ok) {
									const { data: taskResult } = await statusResponse.json();
									if (taskResult?.videoGenerationResponse) {
										handlePollSuccess(
											messageId,
											taskResult.videoGenerationResponse
										);
									} else {
										handlePollFailure('Could not retrieve generated video.');
									}
								} else {
									handlePollFailure('Could not retrieve generated video.');
								}
							} else if (vertexError) {
								handlePollFailure(vertexError || 'Video generation failed.');
							} else {
								// Still in progress
								pollingTimerRef.current = setTimeout(
									pollForResponse,
									pollingInterval
								);
							}
						} else {
							// Handle Fal AI response format (existing logic)
							const { data: taskResult, error: fetchTaskStatusError } = responseData;

							if (fetchTaskStatusError) {
								// console.error('Failed to get task status:', fetchTaskStatusError);
								return;
							}

							const {
								taskStatus,
								videoGenerationResponse,
								videoGenerationResponseError,
							} = taskResult as PollingTaskResponse;

							console.log(
								`Polling attempt ${pollingCountRef.current} for request ${requestId}: Status = ${taskStatus}`
							);

							if (taskStatus === 'COMPLETED') {
								if (!!videoGenerationResponse) {
									const hasVideos = videoGenerationResponse.some(
										(response: Response) =>
											response.requestId === requestId &&
											response.videos?.length
									);

									if (hasVideos) {
										// console.log('Video generation completed successfully');
										handlePollSuccess(messageId, videoGenerationResponse!);
									} else {
										// console.error('Task completed but no videos found in response');
										pollingTimerRef.current = setTimeout(
											pollForResponse,
											pollingInterval / 2
										);
									}
								} else {
									// console.error('Message not found during polling');
									handlePollFailure('Could not retrieve generated video.');
								}
							} else if (taskStatus === 'FAILED' || pollingCountRef.current === 10) {
								// console.log('Video generation task failed');
								handlePollFailure(
									videoGenerationResponseError ||
										'Video generation task failed. Please try again.'
								);
							} else {
								pollingTimerRef.current = setTimeout(
									pollForResponse,
									pollingInterval
								);
							}
						}
					} catch (error) {
						// console.error('Error polling for response:', error);
						pollingTimerRef.current = setTimeout(pollForResponse, pollingInterval);
					}
				};

				pollingTimerRef.current = setTimeout(pollForResponse, pollingInterval);
			},
			[setIsVideoResponseLoading]
		);

		const handlePromptEnhancement = useCallback(
			async (modelName: ModelType) => {
				const model = modelName as VideoModelDisplayName;
				const prompt = input.trim();
				if (!prompt) {
					return;
				}
				try {
					setIsPromptEnhancementInProgress(true);
					const response = await fetch('/api/enhance-prompt', {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json',
						},
						body: JSON.stringify({ prompt, model }),
					});

					if (!response.ok) {
						throw new Error('Failed to enhance prompt');
					}

					const { enhancedPrompt } = await response.json();
					setInput(enhancedPrompt);
				} catch (error: any) {
					// console.error(`Failed to enhance prompt.\nSelected Model:${model}\n${error}`);

					addToast({
						title: 'Failed to enhance prompt',
						description:
							'Failed to enhance prompt. Please try again or proceed with your original prompt.',
						color: 'danger',
					});
				} finally {
					setIsPromptEnhancementInProgress(false);
				}
			},
			[input]
		);

		const handleSubmitPrompt = useCallback(
			async (modelName: ModelType, data: SubmitData) => {
				const model = modelName as VideoModelDisplayName;
				const prompt = input.trim();
				if (!prompt) {
					return;
				}

				saveSnapshot();

				const isNewChat = currentMessages.length === 0;

				updateSelectedModelPrompt({
					prompt: '',
					params: undefined,
				});
				setInput('');
				setIsLoading(true);
				const messageId = nanoid();
				const formData = new FormData();

				formData.append('chatId', id);
				formData.append('messageId', messageId);
				formData.append('model', model);
				formData.append('prompt', prompt);
				formData.append('isNewChat', String(isNewChat));
				formData.append('isSharedChat', String(isSharedChat));
				formData.append('requestType', 'submit');

				if (isSharedChat) {
					formData.append('sharedMessages', JSON.stringify(initialMessages));
				}

				let uploadedFiles: Attachment[] = [];
				if (data.files) {
					Array.from(data.files).forEach((file) => {
						uploadedFiles.push({
							type: file.type,
							name: file.name,
							url: URL.createObjectURL(file),
						});
						formData.append('images', file);
					});
				}

				if (data.params) {
					formData.append('params', JSON.stringify(data.params));
				}

				const newMessage: ChatMessage = {
					chatId: id,
					id: messageId,
					prompt: {
						text: prompt,
						attachments: uploadedFiles,
						params: data.params,
					},
					response: null,
					createdAt: new Date().toISOString(),
				};

				setCurrentMessages((prev) => [...prev, newMessage]);

				try {
					const fetchConfig = {
						method: 'POST',
						body: formData,
						headers: {
							// Don't set Content-Type header when sending FormData
							// The browser will automatically set the correct boundary
						},
					};
					const response = await fetch('/api/video', fetchConfig);

					const { videoGenerationRequestId, uploadedImageUrls, error } =
						await response.json();

					if (error) {
						throw new Error(error);
					}

					setCurrentMessages((prev) =>
						prev.map((msg) =>
							msg.id === messageId
								? {
										...msg,
										prompt: {
											...msg.prompt,
											attachments: uploadedFiles.map((attachment, index) => ({
												...attachment,
												url: uploadedImageUrls[index],
											})),
										},
										response: [
											{
												text: 'Video generation request has been successfully submitted. It will appear here shortly.',
												videos: [],
												modelUsed: {
													modelName:
														videoModelDisplayNameToTechnicalNameMap[
															model
														],
													modelProvider:
														videoModelDisplayNameToProviderMap[model],
												},
												requestId: videoGenerationRequestId,
												createdAt: new Date().toISOString(),
											},
										],
									}
								: msg
						)
					);

					const currentTime = new Date().toISOString();
					if (isNewChat || isSharedChat) {
						window.history.replaceState(null, '', `${origin}/video/${id}`);

						// Create new chat history
						setChatHistory((existingChatHistory) => [
							{
								chatId: id,
								chatType: 'video',
								title: input,
								userId: user.id,
								historyContext: '',
								createdAt: currentTime,
								lastModifiedAt: currentTime,
							},
							...(existingChatHistory || []),
						]);
						setIsSharedChat(false);
					} else {
						// Update last modified time of current chat history
						setChatHistory((existingChatHistory) =>
							(existingChatHistory || []).map((chatHistory) =>
								chatHistory.chatId === id
									? {
											...chatHistory,
											lastModifiedAt: currentTime,
										}
									: chatHistory
							)
						);
					}

					setUpPolling(prompt, messageId, videoGenerationRequestId, model);
				} catch (error) {
					addToast({
						title: 'Error submitting video generation request',
						description: `Model: ${model}. Reason: ${(error as Error).message || 'Unknown error'}`,
						color: 'danger',
					});
					setCurrentMessages(previousMessagesRef.current);
					setInput(prompt);
				} finally {
					setIsLoading(false);
				}
			},
			[
				input,
				saveSnapshot,
				currentMessages.length,
				updateSelectedModelPrompt,
				user.id,
				id,
				isSharedChat,
				initialMessages,
				setUpPolling,
				setChatHistory,
			]
		);

		const handleEditPrompt = useCallback(
			async (
				model: VideoModelDisplayName,
				messageId: string,
				modifiedPrompt: string,
				data: SubmitData
			) => {
				saveSnapshot();
				setIsLoading(true);
				const formData = new FormData();

				formData.append('chatId', id);
				formData.append('messageId', messageId);
				formData.append('model', model);
				formData.append('prompt', modifiedPrompt);
				formData.append('requestType', 'edit');

				if (data.existingAttachments) {
					data.existingAttachments.forEach((attachment) => {
						formData.append('images', JSON.stringify(attachment));
					});
				}

				try {
					// First update the prompt locally
					setCurrentMessages((prev) =>
						prev.map((msg) =>
							msg.id === messageId
								? {
										...msg,
										prompt: {
											...msg.prompt,
											text: modifiedPrompt,
										},
										response: null, // Clear existing response while we wait for new one
									}
								: msg
						)
					);

					const fetchConfig = {
						method: 'POST',
						body: formData,
						headers: {
							// Don't set Content-Type header when sending FormData
							// The browser will automatically set the correct boundary
						},
					};
					const response = await fetch('/api/video', fetchConfig);

					const { videoGenerationRequestId, error } = await response.json();

					if (error) {
						throw new Error(error);
					}

					// Update message with new response
					setCurrentMessages((prev) =>
						prev.map((msg) =>
							msg.id === messageId
								? {
										...msg,
										response: [
											{
												text: 'Video generation request has been successfully submitted. It will appear here shortly.',
												videos: [],
												modelUsed: {
													modelName:
														videoModelDisplayNameToTechnicalNameMap[
															model
														],
													modelProvider:
														videoModelDisplayNameToProviderMap[model],
												},
												requestId: videoGenerationRequestId,
												createdAt: new Date().toISOString(),
											},
										],
									}
								: msg
						)
					);

					setUpPolling(modifiedPrompt, messageId, videoGenerationRequestId, model);

					// Update last modified time of current chat history
					setChatHistory((existingChatHistory) =>
						(existingChatHistory || []).map((chatHistory) =>
							chatHistory.chatId === id
								? {
										...chatHistory,
										lastModifiedAt: new Date().toISOString(),
									}
								: chatHistory
						)
					);
				} catch (error: any) {
					// Parse error for user-friendly message
					let errorMessage = 'Failed to edit and regenerate video. Please try again.';
					try {
						const errorData = JSON.parse(error.message);
						errorMessage = errorData.message || errorMessage;
					} catch {
						// If error.message is not JSON, keep default message
					}

					addToast({
						title: 'Error editing video prompt',
						description: errorMessage,
						color: 'danger',
					});

					setCurrentMessages(previousMessagesRef.current);
				} finally {
					setIsLoading(false);
				}
			},
			[id, saveSnapshot, setChatHistory, setUpPolling]
		);

		const handleRegenerateResponse = useCallback(
			async (messageId: string, model: VideoModelDisplayName, data: SubmitData) => {
				saveSnapshot();
				setIsLoading(true);
				const message = currentMessages.find((msg) => msg.id === messageId);
				if (!message) {
					return;
				}

				const formData = new FormData();
				formData.append('chatId', id);
				formData.append('messageId', messageId);
				formData.append('model', model);
				formData.append('prompt', message.prompt.text);
				formData.append('requestType', 'regenerate');

				if (data.existingAttachments) {
					data.existingAttachments.forEach((attachment) => {
						formData.append('images', JSON.stringify(attachment));
					});
				}

				if (data.params) {
					formData.append('params', JSON.stringify(data.params));
				}

				try {
					const fetchConfig = {
						method: 'POST',
						body: formData,
						headers: {
							// Don't set Content-Type header when sending FormData
							// The browser will automatically set the correct boundary
						},
					};
					const response = await fetch('/api/video', fetchConfig);

					const { videoGenerationRequestId, error } = await response.json();

					if (error) {
						throw new Error(error);
					}

					// Add new response to the message's response array
					setCurrentMessages((prev) =>
						prev.map((msg) =>
							msg.id === messageId
								? {
										...msg,
										response: [
											...(msg.response?.map((resp) => {
												return { ...resp, isActive: false };
											}) || []),
											{
												text: 'Video generation request has been successfully submitted. It will appear here shortly.',
												videos: [],
												modelUsed: {
													modelName:
														videoModelDisplayNameToTechnicalNameMap[
															model
														],
													modelProvider:
														videoModelDisplayNameToProviderMap[model],
												},
												requestId: videoGenerationRequestId,
												createdAt: new Date().toISOString(),
											},
										],
									}
								: msg
						)
					);

					setUpPolling(message.prompt.text, messageId, videoGenerationRequestId, model);

					// Update last modified time of current chat history
					setChatHistory((existingChatHistory) =>
						(existingChatHistory || []).map((chatHistory) =>
							chatHistory.chatId === id
								? {
										...chatHistory,
										lastModifiedAt: new Date().toISOString(),
									}
								: chatHistory
						)
					);

					updateActiveModel(model);
					setInput('');
				} catch (error: any) {
					// Parse error for user-friendly message
					let errorMessage = 'Failed to regenerate video. Please try again.';
					try {
						const errorData = JSON.parse(error.message);
						errorMessage = errorData.message || errorMessage;
					} catch {
						// If error.message is not JSON, keep default message
					}

					addToast({
						title: 'Error regenerating video',
						description: errorMessage,
						color: 'danger',
					});

					setCurrentMessages(previousMessagesRef.current);
				} finally {
					setIsLoading(false);
				}
			},
			[currentMessages, id, saveSnapshot, setChatHistory, setUpPolling, updateActiveModel]
		);

		const handleNavigate = useCallback((messageId: string, targetIndex: number) => {
			setCurrentMessages((prev) =>
				prev.map((msg) =>
					msg.id === messageId
						? {
								...msg,
								response:
									msg.response?.map((response, index) => {
										if (index === targetIndex) {
											return { ...response, isActive: true };
										} else {
											return { ...response, isActive: false };
										}
									}) || null,
							}
						: msg
				)
			);
		}, []);

		useEffect(() => {
			updateSharedChatDetails({
				type: 'video',
				chatId: id,
				isShareable: !isSharedChat && currentMessages.length > 0,
			});
		}, [currentMessages.length, id, isSharedChat, updateSharedChatDetails]);

		const PromptSectionComponent = (
			<PromptSection
				type="video"
				input={input}
				isLoading={isLoading}
				setInput={setInput}
				onSubmit={handleSubmitPrompt}
				isPromptEnhancementInProgress={isPromptEnhancementInProgress}
				onPromptEnhancement={handlePromptEnhancement}
			/>
		);

		return (
			<div className="flex h-full w-full max-w-3xl flex-col">
				<AnimatePresence mode="wait">
					{currentMessages.length === 0 ? (
						<motion.div
							key="dashboard"
							className="flex h-full flex-col"
							exit={{ opacity: 0 }}
							transition={{ duration: 0.2 }}
						>
							<div className="mb-12 flex flex-1 flex-col items-center justify-center space-y-8">
								<div className="flex flex-col items-center justify-center space-y-4">
									<div className="group relative rounded-2xl border-1.5 border-zeco-purple bg-black p-3 transition duration-300 ease-in-out">
										<Logo
											size={36}
											fill="#ccc"
											className="flex md:hidden"
										/>
										<Logo
											size={48}
											fill="#ccc"
											className="hidden md:flex"
										/>
									</div>
									<TypewriterEffectSmooth
										words={[
											{
												text: 'Everything',
												className: 'text-zinc-800 dark:text-zinc-200',
											},
											{
												text: 'AI',
												className: 'text-zinc-800 dark:text-zinc-200',
											},
											{
												text: 'for',
												className: 'text-zinc-800 dark:text-zinc-200',
											},
											{
												text: 'YOU.',
												className: 'text-gradient-primary font-bold',
											},
										]}
										className="text-center"
									/>
								</div>

								<div className="mx-auto w-full max-w-2xl px-4">
									{PromptSectionComponent}
								</div>
							</div>

							<div className="mb-6 flex w-full items-center justify-center">
								<FloatingDock />
							</div>
						</motion.div>
					) : (
						<motion.div
							key="video"
							className="flex h-full w-full flex-1 flex-col"
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							exit={{ opacity: 0, y: -20 }}
							transition={{ duration: 0.2, ease: 'easeInOut' }}
						>
							<main className="flex w-full flex-1 flex-col overflow-y-auto">
								<VideoGenerationPanel
									messages={currentMessages}
									isLoading={isLoading}
									isVideoResponseLoading={isVideoResponseLoading}
									isSharedChat={isSharedChat}
									onEdit={handleEditPrompt}
									onRegenerate={handleRegenerateResponse}
									onNavigate={handleNavigate}
								/>
							</main>
							<footer className="flex w-full flex-col px-4 pb-4 md:px-6">
								{PromptSectionComponent}
								<div className="relative mt-2 w-full">
									<p className="text-center text-xs text-white/40">
										These models can generate inaccurate videos. Consider
										reviewing them before use.
									</p>
								</div>
							</footer>
						</motion.div>
					)}
				</AnimatePresence>
			</div>
		);
	}
);

VideoGenerationLayout.displayName = 'VideoGenerationLayout';
export default VideoGenerationLayout;
