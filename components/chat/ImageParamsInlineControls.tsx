import Image from 'next/image';
import { Button } from '@heroui/button';
import {
	Dropdown,
	DropdownSection,
	DropdownTrigger,
	DropdownMenu,
	DropdownItem,
} from '@heroui/dropdown';
import {
	IMAGE_MODEL_CONSTRAINTS,
	ImageGenerationParams,
} from '@/models/image/image-generation-constraints';
import { ImageModelDisplayName } from '@/models/image/image-generation-models';
import {
	IconPalette,
	IconStar,
	IconAspectRatio,
	IconSquare,
	IconRectangleVertical,
	IconRectangle,
	IconBoxMultiple,
	IconPhotoAi,
	IconSettings,
	IconRocket,
	IconClock,
	IconMicroscope,
	IconBrain,
	IconWand,
	IconTarget,
	IconBackground,
	IconColorSwatch,
} from '@tabler/icons-react';

interface ImageParamsInlineControlsProps {
	model: ImageModelDisplayName;
	params: ImageGenerationParams;
	onChange: (newParams: Partial<ImageGenerationParams>) => void;
}

const ImageParamsInlineControls = ({ model, params, onChange }: ImageParamsInlineControlsProps) => {
	const modelConstraints = IMAGE_MODEL_CONSTRAINTS[model];

	// The model context might not have been updated yet, so we need to check if the model constraints are available.
	if (!modelConstraints) {
		return null;
	}

	const handleParamChange = (key: keyof ImageGenerationParams, value: any) => {
		onChange({ [key]: value });
	};

	const renderDropdownTrigger = (
		label: string,
		value: string | number | undefined,
		icon: React.ReactNode
	) => (
		<Button
			disableRipple
			variant="light"
			size="sm"
			radius="full"
			className={`gap-1 whitespace-nowrap px-2 text-xs sm:px-3 ${
				value
					? 'border-blue-200 bg-blue-50 text-blue-500 dark:border-blue-900 dark:bg-blue-950 dark:text-blue-400'
					: 'text-white/70 hover:bg-white/5 hover:text-white/90'
			}`}
			startContent={icon}
		>
			{value ? (label === 'Variations' ? `${value}v` : `${value}`) : label}
		</Button>
	);

	const getInferenceStepsLabel = (steps: number) => {
		if (steps <= 20) return 'Fast';
		if (steps <= 35) return 'Balanced';
		return 'Detailed';
	};

	const getInferenceStepsValue = (label: string) => {
		switch (label) {
			case 'Fast':
				return 20;
			case 'Balanced':
				return 35;
			case 'Detailed':
				return 50;
			default:
				return undefined;
		}
	};

	const getGuidanceScaleLabel = (scale: number) => {
		if (scale <= 7) {
			return 'Creative';
		}
		if (scale <= 13) {
			return 'Balanced';
		}
		return 'Precise';
	};

	const getGuidanceScaleValue = (label: string) => {
		switch (label) {
			case 'Creative':
				return 7;
			case 'Balanced':
				return 13;
			case 'Precise':
				return 20;
			default:
				return undefined;
		}
	};

	return (
		<div className="flex max-w-full flex-row items-center gap-1 overflow-x-auto scrollbar-thin scrollbar-thumb-gray-600 sm:gap-2">
			{modelConstraints.n && modelConstraints.n.max > 1 && (
				<Dropdown placement="top-start">
					<DropdownTrigger>
						{renderDropdownTrigger(
							'Variations',
							params.n,
							<IconBoxMultiple size={16} />
						)}
					</DropdownTrigger>
					<DropdownMenu
						aria-label="Number of variations selection"
						selectionMode="single"
						selectedKeys={params.n ? [String(params.n)] : []}
						onSelectionChange={(keys) =>
							handleParamChange('n', Number(Array.from(keys)[0]) || undefined)
						}
					>
						<DropdownSection title="Number of Variations">
							{Array.from(
								Array(modelConstraints.n.max - modelConstraints.n.min + 1).keys()
							).map((i) => {
								const count = i + modelConstraints.n.min;
								return (
									<DropdownItem
										key={String(count)}
										textValue={`${count} ${count === 1 ? 'image' : 'images'}`}
										startContent={<IconPhotoAi size={16} />}
									>
										{count} {count === 1 ? 'image' : 'images'}
									</DropdownItem>
								);
							})}
						</DropdownSection>
					</DropdownMenu>
				</Dropdown>
			)}

			{modelConstraints.style && (
				<Dropdown placement="top-start">
					<DropdownTrigger>
						{renderDropdownTrigger(
							'Style',
							modelConstraints.style.find((s) => s.value === params.style)?.label ??
								params.style,
							<IconPalette size={16} />
						)}
					</DropdownTrigger>
					<DropdownMenu
						aria-label="Style selection"
						selectionMode="single"
						selectedKeys={params.style ? [params.style] : []}
						onSelectionChange={(keys) =>
							handleParamChange('style', Array.from(keys)[0] || undefined)
						}
					>
						<DropdownSection
							title="Image Style"
							classNames={{
								group: 'max-h-[50vh] overflow-y-auto',
							}}
						>
							{modelConstraints.style.map((styleOption) => (
								<DropdownItem
									key={styleOption.value}
									textValue={styleOption.label}
									title={styleOption.label}
									description={styleOption.description}
									startContent={
										styleOption.exampleImage && (
											<div className="relative mr-4 h-32 w-32 shrink-0 overflow-hidden rounded">
												<Image
													src={styleOption.exampleImage}
													alt={styleOption.label}
													fill
													className="object-cover"
													sizes="128px"
												/>
											</div>
										)
									}
								/>
							))}
						</DropdownSection>
					</DropdownMenu>
				</Dropdown>
			)}

			{modelConstraints.background && (
				<Dropdown placement="top-start">
					<DropdownTrigger>
						{renderDropdownTrigger(
							'Background',
							modelConstraints.background.find((b) => b.value === params.background)
								?.label ?? params.background,
							<IconBackground size={16} />
						)}
					</DropdownTrigger>
					<DropdownMenu
						aria-label="Background selection"
						selectionMode="single"
						selectedKeys={params.background ? [params.background] : []}
						onSelectionChange={(keys) =>
							handleParamChange('background', Array.from(keys)[0] || undefined)
						}
					>
						<DropdownSection
							title="Image Background"
							classNames={{
								base: 'space-y-2',
								group: 'max-h-[50vh] overflow-y-auto',
							}}
						>
							{modelConstraints.background.map((backgroundOption) => (
								<DropdownItem
									key={backgroundOption.value}
									textValue={backgroundOption.label}
									title={backgroundOption.label}
									startContent={
										backgroundOption.exampleImage && (
											<div className="relative mr-4 h-32 w-32 shrink-0 overflow-hidden rounded">
												<Image
													src={backgroundOption.exampleImage}
													alt={backgroundOption.label}
													fill
													className="object-cover"
													sizes="128px"
												/>
											</div>
										)
									}
								/>
							))}
						</DropdownSection>
					</DropdownMenu>
				</Dropdown>
			)}

			{modelConstraints.quality && (
				<Dropdown placement="top-start">
					<DropdownTrigger>
						{renderDropdownTrigger('Quality', params.quality, <IconStar size={16} />)}
					</DropdownTrigger>
					<DropdownMenu
						aria-label="Quality selection"
						selectionMode="single"
						selectedKeys={params.quality ? [params.quality] : []}
						onSelectionChange={(keys) =>
							handleParamChange('quality', Array.from(keys)[0] || undefined)
						}
					>
						<DropdownSection title="Image Quality">
							{modelConstraints.quality.map((value) => (
								<DropdownItem key={value}>
									{value.charAt(0).toUpperCase() + value.slice(1).toLowerCase()}
								</DropdownItem>
							))}
						</DropdownSection>
					</DropdownMenu>
				</Dropdown>
			)}

			{modelConstraints.aspectRatio && (
				<Dropdown placement="top-start">
					<DropdownTrigger>
						{renderDropdownTrigger(
							'Aspect Ratio',
							params.aspectRatio,
							<IconAspectRatio size={16} />
						)}
					</DropdownTrigger>
					<DropdownMenu
						aria-label="Aspect ratio selection"
						selectionMode="single"
						selectedKeys={params.aspectRatio ? [params.aspectRatio] : []}
						onSelectionChange={(keys) =>
							handleParamChange('aspectRatio', Array.from(keys)[0] || undefined)
						}
					>
						<DropdownSection title="Aspect Ratio">
							{modelConstraints.aspectRatio.allowedRatios.map((value) => {
								const [width, height] = value.split(':').map(Number);
								const icon =
									width === height ? (
										<IconSquare size={16} />
									) : width > height ? (
										<IconRectangle size={16} />
									) : (
										<IconRectangleVertical size={16} />
									);
								return (
									<DropdownItem
										key={value}
										startContent={icon}
									>
										{value}
									</DropdownItem>
								);
							})}
						</DropdownSection>
					</DropdownMenu>
				</Dropdown>
			)}

			{modelConstraints.size && (
				<Dropdown placement="top-start">
					<DropdownTrigger>
						{renderDropdownTrigger('Size', params.size, <IconAspectRatio size={16} />)}
					</DropdownTrigger>
					<DropdownMenu
						aria-label="Size selection"
						selectionMode="single"
						selectedKeys={params.size ? [params.size] : []}
						onSelectionChange={(keys) =>
							handleParamChange('size', Array.from(keys)[0] || undefined)
						}
					>
						<DropdownSection title="Image Size">
							{modelConstraints.size.allowedSizes.map((value) => {
								const [width, height] = value.split('x').map(Number);
								const icon =
									width === height ? (
										<IconSquare size={16} />
									) : width > height ? (
										<IconRectangle size={16} />
									) : (
										<IconRectangleVertical size={16} />
									);
								return (
									<DropdownItem
										key={value}
										startContent={icon}
									>
										{value}
									</DropdownItem>
								);
							})}
						</DropdownSection>
					</DropdownMenu>
				</Dropdown>
			)}

			{modelConstraints.image_size && (
				<Dropdown placement="top-start">
					<DropdownTrigger>
						{renderDropdownTrigger(
							'Image Size',
							params.image_size
								? params.image_size
										.replace('_', ' ')
										.split(' ')
										.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
										.join(' ')
								: undefined,
							<IconAspectRatio size={16} />
						)}
					</DropdownTrigger>
					<DropdownMenu
						aria-label="Image size selection"
						selectionMode="single"
						selectedKeys={params.image_size ? [params.image_size] : []}
						onSelectionChange={(keys) =>
							handleParamChange('image_size', Array.from(keys)[0] || undefined)
						}
					>
						<DropdownSection title="Image Size">
							{modelConstraints.image_size.map((value) => {
								// Format the display value to be more user-friendly
								const displayValue = value
									.replace('_', ' ')
									.split(' ')
									.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
									.join(' ');

								// Determine icon based on the size type
								let icon = <IconSquare size={16} />;
								if (value.includes('portrait')) {
									icon = <IconRectangleVertical size={16} />;
								} else if (value.includes('landscape')) {
									icon = <IconRectangle size={16} />;
								}

								return (
									<DropdownItem
										key={value}
										startContent={icon}
									>
										{displayValue}
									</DropdownItem>
								);
							})}
						</DropdownSection>
					</DropdownMenu>
				</Dropdown>
			)}

			{modelConstraints.color_palette && (
				<Dropdown placement="top-start">
					<DropdownTrigger>
						{renderDropdownTrigger(
							'Color Palette',
							modelConstraints.color_palette.find(
								(c) => c.value === params.color_palette
							)?.label ?? params.color_palette,
							<IconColorSwatch size={16} />
						)}
					</DropdownTrigger>
					<DropdownMenu
						aria-label="Color palette selection"
						selectionMode="single"
						selectedKeys={params.color_palette ? [params.color_palette] : []}
						onSelectionChange={(keys) =>
							handleParamChange('color_palette', Array.from(keys)[0] || undefined)
						}
					>
						<DropdownSection
							title="Color Palette"
							classNames={{
								group: 'max-h-[50vh] overflow-y-auto',
							}}
						>
							{modelConstraints.color_palette.map((colorOption) => (
								<DropdownItem
									key={colorOption.value}
									textValue={colorOption.label}
									title={colorOption.label}
									description={colorOption.description}
									startContent={
										<div className="flex -space-x-1 overflow-hidden">
											{colorOption.colors?.map((color, colorIndex) => (
												<div
													key={colorIndex}
													className="h-5 w-5 rounded-full"
													style={{ backgroundColor: color }}
												/>
											))}
										</div>
									}
								/>
							))}
						</DropdownSection>
					</DropdownMenu>
				</Dropdown>
			)}

			{modelConstraints.num_inference_steps && (
				<Dropdown placement="top-start">
					<DropdownTrigger>
						{renderDropdownTrigger(
							'Detail',
							params.num_inference_steps
								? getInferenceStepsLabel(params.num_inference_steps)
								: undefined,
							<IconSettings size={20} />
						)}
					</DropdownTrigger>
					<DropdownMenu
						aria-label="Detail level selection"
						selectionMode="single"
						selectedKeys={
							params.num_inference_steps
								? [getInferenceStepsLabel(params.num_inference_steps)]
								: []
						}
						onSelectionChange={(keys) => {
							const selectedLabel = Array.from(keys)[0] as string;
							handleParamChange(
								'num_inference_steps',
								getInferenceStepsValue(selectedLabel)
							);
						}}
					>
						<DropdownSection title="Image Detail Level">
							<DropdownItem
								key="Fast"
								textValue="Fast - Quick generation with basic details"
								description="Quick generation with basic details"
								startContent={
									<IconRocket
										size={20}
										className="text-blue-500"
									/>
								}
							>
								Fast
							</DropdownItem>
							<DropdownItem
								key="Balanced"
								textValue="Balanced - Good balance of speed and quality"
								description="Good balance of speed and quality"
								startContent={
									<IconClock
										size={20}
										className="text-green-500"
									/>
								}
							>
								Balanced
							</DropdownItem>
							<DropdownItem
								key="Detailed"
								textValue="Detailed - Highest quality with more processing time"
								description="Highest quality with more processing time"
								startContent={
									<IconMicroscope
										size={20}
										className="text-purple-500"
									/>
								}
							>
								Detailed
							</DropdownItem>
						</DropdownSection>
					</DropdownMenu>
				</Dropdown>
			)}

			{modelConstraints.guidance_scale && (
				<Dropdown placement="top-start">
					<DropdownTrigger>
						{renderDropdownTrigger(
							'Strictness',
							params.guidance_scale
								? getGuidanceScaleLabel(params.guidance_scale)
								: undefined,
							<IconBrain size={20} />
						)}
					</DropdownTrigger>
					<DropdownMenu
						aria-label="Prompt adherence selection"
						selectionMode="single"
						selectedKeys={
							params.guidance_scale
								? [getGuidanceScaleLabel(params.guidance_scale)]
								: []
						}
						onSelectionChange={(keys) => {
							const selectedLabel = Array.from(keys)[0] as string;
							handleParamChange(
								'guidance_scale',
								getGuidanceScaleValue(selectedLabel)
							);
						}}
					>
						<DropdownSection title="Prompt Adherence">
							<DropdownItem
								key="Creative"
								textValue="Creative - More artistic freedom"
								description="More artistic freedom"
								startContent={
									<IconWand
										size={20}
										className="text-yellow-500"
									/>
								}
							>
								Creative
							</DropdownItem>
							<DropdownItem
								key="Balanced"
								textValue="Balanced - Moderate adherence to prompt"
								description="Moderate adherence to prompt"
								startContent={
									<IconBrain
										size={20}
										className="text-green-500"
									/>
								}
							>
								Balanced
							</DropdownItem>
							<DropdownItem
								key="Precise"
								textValue="Precise - Strictly follows the prompt"
								description="Strictly follows the prompt"
								startContent={
									<IconTarget
										size={20}
										className="text-red-500"
									/>
								}
							>
								Precise
							</DropdownItem>
						</DropdownSection>
					</DropdownMenu>
				</Dropdown>
			)}
		</div>
	);
};

export default ImageParamsInlineControls;
