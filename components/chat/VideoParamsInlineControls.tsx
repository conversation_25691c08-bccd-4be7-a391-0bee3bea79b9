import {
	IconAspectRatio,
	IconSquare,
	IconRectangleVertical,
	IconRectangle,
	IconClock,
	IconDeviceTv,
	IconVolume,
} from '@tabler/icons-react';
import { Button } from '@heroui/button';
import {
	Dropdown,
	DropdownSection,
	DropdownTrigger,
	DropdownMenu,
	DropdownItem,
} from '@heroui/dropdown';
import {
	VIDEO_MODEL_CONSTRAINTS,
	VideoGenerationParams,
} from '@/models/video/video-generation-constraints';
import { VideoModelDisplayName } from '@/models/video/video-generation-models';

interface VideoParamsInlineControlsProps {
	model: VideoModelDisplayName;
	params: VideoGenerationParams;
	onChange: (newParams: Partial<VideoGenerationParams>) => void;
}

const VideoParamsInlineControls = ({ model, params, onChange }: VideoParamsInlineControlsProps) => {
	const modelConstraints = VIDEO_MODEL_CONSTRAINTS[model];

	// The model context might not have been updated yet, so we need to check if the model constraints are available.
	if (!modelConstraints) {
		return null;
	}

	const handleParamChange = (key: keyof VideoGenerationParams, value: any) => {
		onChange({ [key]: value });
	};

	const renderDropdownTrigger = (
		label: string,
		value: string | number | undefined,
		icon: React.ReactNode
	) => (
		<Button
			disableRipple
			variant="light"
			size="sm"
			radius="full"
			className={`gap-1 whitespace-nowrap px-2 text-xs sm:px-3 ${
				value
					? 'border-blue-200 bg-blue-50 text-blue-500 dark:border-blue-900 dark:bg-blue-950 dark:text-blue-400'
					: 'text-white/70 hover:bg-white/5 hover:text-white/90'
			}`}
			startContent={icon}
		>
			{value ? `${value}` : label}
		</Button>
	);

	return (
		<div className="flex max-w-full flex-row items-center gap-1 overflow-x-auto scrollbar-thin scrollbar-thumb-gray-600 sm:gap-2">
			{modelConstraints.aspectRatio && (
				<Dropdown placement="top-start">
					<DropdownTrigger>
						{renderDropdownTrigger(
							'Aspect Ratio',
							params.aspectRatio,
							<IconAspectRatio size={16} />
						)}
					</DropdownTrigger>
					<DropdownMenu
						aria-label="Aspect ratio selection"
						selectionMode="single"
						selectedKeys={params.aspectRatio ? [params.aspectRatio] : []}
						onSelectionChange={(keys) =>
							handleParamChange('aspectRatio', Array.from(keys)[0] || undefined)
						}
					>
						<DropdownSection title="Aspect Ratio">
							{modelConstraints.aspectRatio.map((value) => {
								const [width, height] = value.split(':').map(Number);
								const icon =
									width === height ? (
										<IconSquare size={16} />
									) : width > height ? (
										<IconRectangle size={16} />
									) : (
										<IconRectangleVertical size={16} />
									);
								return (
									<DropdownItem
										key={value}
										startContent={icon}
									>
										{value}
									</DropdownItem>
								);
							})}
						</DropdownSection>
					</DropdownMenu>
				</Dropdown>
			)}

			{modelConstraints.duration && (
				<Dropdown placement="top-start">
					<DropdownTrigger>
						{renderDropdownTrigger(
							'Duration',
							params.duration,
							<IconClock size={16} />
						)}
					</DropdownTrigger>
					<DropdownMenu
						aria-label="Duration selection"
						selectionMode="single"
						selectedKeys={params.duration ? [params.duration] : []}
						onSelectionChange={(keys) =>
							handleParamChange('duration', Array.from(keys)[0] || undefined)
						}
					>
						<DropdownSection title="Video Duration (sec)">
							{modelConstraints.duration.map((value) => (
								<DropdownItem
									key={value}
									startContent={<IconClock size={16} />}
								>
									{value}
								</DropdownItem>
							))}
						</DropdownSection>
					</DropdownMenu>
				</Dropdown>
			)}

			{modelConstraints.resolution && (
				<Dropdown placement="top-start">
					<DropdownTrigger>
						{renderDropdownTrigger(
							'Resolution',
							params.resolution,
							<IconDeviceTv size={16} />
						)}
					</DropdownTrigger>
					<DropdownMenu
						aria-label="Resolution selection"
						selectionMode="single"
						selectedKeys={params.resolution ? [params.resolution] : []}
						onSelectionChange={(keys) =>
							handleParamChange('resolution', Array.from(keys)[0] || undefined)
						}
					>
						<DropdownSection title="Video Resolution">
							{modelConstraints.resolution.map((value) => (
								<DropdownItem
									key={value}
									startContent={<IconDeviceTv size={16} />}
								>
									{value}
								</DropdownItem>
							))}
						</DropdownSection>
					</DropdownMenu>
				</Dropdown>
			)}

			{modelConstraints.generateAudio && (
				<Button
					disableRipple
					variant="light"
					size="sm"
					radius="full"
					className={`gap-1 text-xs ${
						params.generateAudio
							? 'border-blue-200 bg-blue-50 text-blue-500 dark:border-blue-900 dark:bg-blue-950 dark:text-blue-400'
							: 'text-white/70 hover:bg-white/5 hover:text-white/90'
					}`}
					startContent={<IconVolume size={16} />}
					onPress={() => handleParamChange('generateAudio', !params.generateAudio)}
				>
					{params.generateAudio ? 'Audio On' : 'Audio Off'}
				</Button>
			)}
		</div>
	);
};

export default VideoParamsInlineControls;
