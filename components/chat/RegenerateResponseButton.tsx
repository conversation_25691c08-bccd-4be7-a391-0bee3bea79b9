import { useState } from 'react';
import { Button } from '@heroui/button';
import { useDisclosure } from '@heroui/modal';
import { IconChevronDown } from '@tabler/icons-react';
import { ModelType } from '@/app/(post-auth)/providers';
import {
	Conversational<PERSON>odelsList,
	ModelProvider,
} from '@/models/conversational/conversational-models';
import {
	ImageModelDisplayName,
	ImageModelProvider,
	ImageModelsList,
} from '@/models/image/image-generation-models';
import { ImageGenerationParams } from '@/models/image/image-generation-constraints';
import {
	VideoModelDisplayName,
	VideoModelProvider,
	VideoModelsList,
} from '@/models/video/video-generation-models';
import { GeneratedImageDetails } from '@/models/image/image-generation-examples';
import { getProviderIcon } from '../../utils/model-selection-utils';
import ModelSelectionDialog from './model-selection/ModelSelectionDialog';
import ImageParamsModal from './ImageParamsModal';
import VideoParamsModal from './VideoParamsModal';
import { VIDEO_MODEL_CONSTRAINTS } from '@/models/video/video-generation-constraints';
import { SubmitData } from './PromptSection';
import { Tooltip } from '@heroui/tooltip';
import { ChatType } from '@/types/chat';
import ModelSelectionDropdown from './model-selection/ModelSelectionDropdown';
import MediaModelSelectionDropdown from './model-selection/MediaModelSelectionDropdown';

interface RegenerateResponseButtonProps {
	type: ChatType;
	modelName: ModelType;
	modelProvider: ModelProvider | ImageModelProvider | VideoModelProvider;
	onRegenerate: (model: ModelType, data: SubmitData) => void;
}

const RegenerateResponseButton = ({
	type,
	modelName,
	modelProvider,
	onRegenerate,
}: RegenerateResponseButtonProps) => {
	const [selectedModel, setSelectedModel] = useState<ModelType>(modelName);
	const [defaultParams, setDefaultParams] = useState<ImageGenerationParams>();
	const modelDialogDisclosure = useDisclosure();
	const paramsDialogDisclosure = useDisclosure();

	const handleModelSelect = (model: ModelType, example?: GeneratedImageDetails) => {
		setSelectedModel(model);
		if (example) {
			setDefaultParams(example.parameters);
		}
		modelDialogDisclosure.onClose();

		if (type === 'chat') {
			onRegenerate(model, { params: {} });
		} else {
			if (type === 'video') {
				const modelConstraints = VIDEO_MODEL_CONSTRAINTS[model as VideoModelDisplayName];
				if (Object.keys(modelConstraints).length === 0) {
					onRegenerate(model, { params: {} });
					return;
				}
			}
			paramsDialogDisclosure.onOpen();
		}
	};

	const handleParamsSubmit = (params: any) => {
		onRegenerate(selectedModel, { params });
		paramsDialogDisclosure.onClose();
	};

	const getModelsForType = (type: ChatType) => {
		if (type === 'image') {
			return ImageModelsList;
		} else if (type === 'video') {
			return VideoModelsList;
		} else {
			return ConversationalModelsList;
		}
	};

	return (
		<>
			{type === 'chat' ? (
				<Tooltip
					content="Regenerate with different model"
					placement="bottom"
					size="sm"
					radius="sm"
					className="bg-gray-50 text-[#090909]"
					delay={500}
					closeDelay={0}
				>
					<Button
						disableRipple
						size="sm"
						variant="light"
						radius="md"
						startContent={getProviderIcon(modelProvider, type, 16)}
						onPress={modelDialogDisclosure.onOpen}
						className="flex flex-row items-center gap-1 whitespace-nowrap text-xs"
					>
						{modelName}
						<IconChevronDown size={12} />
					</Button>
				</Tooltip>
			) : (
				<>
					{/* Desktop: Button with tooltip */}
					<div className="hidden md:flex">
						<MediaModelSelectionDropdown
							type={type}
							models={getModelsForType(type) as any}
							selectedModel={modelName as any}
							selectedModelProvider={modelProvider as any}
							onModelSelect={handleModelSelect}
							isRegenerate={true}
						/>
					</div>
					{/* Mobile: ModelSelectionDropdown */}
					<div className="flex md:hidden">
						<ModelSelectionDropdown
							type={type as 'image' | 'video'}
							models={getModelsForType(type) as any}
							selectedModel={modelName}
							selectedModelProvider={modelProvider}
							onModelSelect={(model: string) => handleModelSelect(model as ModelType)}
							isRegenerate={true}
						/>
					</div>
				</>
			)}

			<ModelSelectionDialog
				isOpen={modelDialogDisclosure.isOpen}
				onOpenChange={modelDialogDisclosure.onOpenChange}
				type={type}
				models={getModelsForType(type)}
				selectedModel={selectedModel || modelName}
				onModelSelect={handleModelSelect}
			/>

			{type === 'image' && (
				<ImageParamsModal
					model={selectedModel as ImageModelDisplayName}
					isOpen={paramsDialogDisclosure.isOpen}
					onOpenChange={paramsDialogDisclosure.onOpenChange}
					onApply={handleParamsSubmit}
					defaultParams={defaultParams}
				/>
			)}

			{type === 'video' && (
				<VideoParamsModal
					model={selectedModel as VideoModelDisplayName}
					isOpen={paramsDialogDisclosure.isOpen}
					onOpenChange={paramsDialogDisclosure.onOpenChange}
					onApply={handleParamsSubmit}
					defaultParams={defaultParams}
				/>
			)}
		</>
	);
};

export default RegenerateResponseButton;
