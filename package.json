{"name": "zeco-ai", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,css,md}\"", "prepare": "husky"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md,mdx,html,yml,yaml,scss,sass}": ["prettier --write"]}, "engines": {"pnpm": ">=9.2.0", "node": ">=20.12.0"}, "dependencies": {"@ai-sdk/fal": "^0.0.2", "@ai-sdk/google-vertex": "^2.2.23", "@ai-sdk/mistral": "^1.2.1", "@ai-sdk/openai": "^1.3.3", "@ai-sdk/togetherai": "^0.1.18", "@ai-sdk/xai": "^1.2.15", "@fal-ai/client": "^1.4.0", "@heroui/accordion": "2.2.21", "@heroui/button": "^2.2.24", "@heroui/card": "^2.2.23", "@heroui/chip": "^2.2.20", "@heroui/code": "^2.2.18", "@heroui/divider": "^2.2.17", "@heroui/drawer": "^2.2.21", "@heroui/dropdown": "^2.3.24", "@heroui/image": "^2.2.15", "@heroui/input": "^2.4.25", "@heroui/link": "^2.2.21", "@heroui/modal": "^2.2.21", "@heroui/progress": "^2.2.20", "@heroui/scroll-shadow": "^2.3.16", "@heroui/select": "^2.4.25", "@heroui/slider": "^2.4.21", "@heroui/spinner": "^2.2.21", "@heroui/switch": "^2.2.22", "@heroui/system": "^2.4.20", "@heroui/tabs": "^2.2.21", "@heroui/theme": "^2.4.20", "@heroui/toast": "^2.0.14", "@heroui/tooltip": "^2.2.21", "@lobehub/icons": "^1.94.0", "@mendable/firecrawl-js": "^1.29.1", "@react-aria/ssr": "^3.9.7", "@react-aria/visually-hidden": "^3.8.21", "@supabase/auth-js": "^2.69.1", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.49.3", "@tabler/icons-react": "^3.31.0", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "ai": "^4.2.6", "clsx": "^2.1.1", "creem": "^0.3.37", "framer-motion": "^12.23.12", "google-auth-library": "^10.2.0", "katex": "^0.16.22", "libsodium-wrappers": "^0.7.15", "mini-svg-data-uri": "^1.4.4", "next": "15.5.0", "next-pwa": "^5.6.0", "next-themes": "^0.2.1", "nodemailer": "^6.10.0", "openai": "^5.10.2", "react": "19.1.1", "react-dom": "19.1.1", "react-markdown": "^9.1.0", "react-syntax-highlighter": "^15.6.1", "rehype-katex": "^7.0.1", "rehype-prism": "^2.3.3", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "standardwebhooks": "^1.0.0", "tailwind-merge": "^2.6.0", "tailwind-scrollbar": "^3.1.0", "tailwind-variants": "^0.2.1", "uuid": "^10.0.0", "zod": "^3.24.2"}, "devDependencies": {"@types/libsodium-wrappers": "^0.7.14", "@types/node": "^20.17.27", "@types/nodemailer": "^6.4.17", "@types/react": "19.1.10", "@types/react-dom": "19.1.7", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "eslint": "^8.57.1", "eslint-config-next": "15.5.0", "eslint-config-prettier": "^10.1.1", "husky": "^9.1.7", "lint-staged": "^15.5.0", "postcss": "^8.5.3", "prettier": "3.5.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.12", "typescript": "^5.8.2"}, "pnpm": {"onlyBuiltDependencies": ["@heroui/shared-utils", "@tsparticles/engine", "@vercel/speed-insights", "sharp"], "overrides": {"@types/react": "19.1.10", "@types/react-dom": "19.1.7"}}, "packageManager": "pnpm@10.12.1+sha512.f0dda8580f0ee9481c5c79a1d927b9164f2c478e90992ad268bbb2465a736984391d6333d2c327913578b2804af33474ca554ba29c04a8b13060a717675ae3ac"}